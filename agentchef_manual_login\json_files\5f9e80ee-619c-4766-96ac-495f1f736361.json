{"name": "Very quick quickstart", "nodes": [{"id": "cbb6afcc-f900-434d-ad2e-affb31ccf7a9", "name": "Customer Datastore", "type": "n8n-nodes-base.n8nTrainingCustomerDatastore", "position": [1000, 740], "parameters": {"operation": "getAllPeople", "returnAll": true}, "typeVersion": 1}, {"id": "1eb939c0-e391-4e3b-9751-889da2de7cf7", "name": "Note", "type": "n8n-nodes-base.stickyNote", "position": [460, 460], "parameters": {"width": 300, "height": 220, "content": "## About the very quick quickstart workflow\n\nThis is an incomplete workflow, used in the [very quick quickstart](https://docs.n8n.io//try-it-out/quickstart/) tutorial."}, "typeVersion": 1}, {"id": "c53a8591-9efe-4fb8-993b-6cc309f3240e", "name": "Note1", "type": "n8n-nodes-base.stickyNote", "position": [940, 640], "parameters": {"width": 220, "height": 300, "content": "**Get fake sample data**"}, "typeVersion": 1}, {"id": "c7e35ca4-b180-4280-9e43-a5dda5d3ea97", "name": "Note2", "type": "n8n-nodes-base.stickyNote", "position": [1220, 640], "parameters": {"width": 220, "height": 300, "content": "**Extract data and prepare it for use in the next node**"}, "typeVersion": 1}, {"id": "94bba884-5cef-4fe6-ba7d-cc7dbe49839c", "name": "When clicking \"Test Workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [760, 740], "parameters": {}, "typeVersion": 1}, {"id": "f6d22d64-c77f-415d-9c34-c7106ba4877a", "name": "Edit Fields1", "type": "n8n-nodes-base.set", "position": [1280, 740], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "df041e3c-fc09-4ba2-8e6b-37f2c6a02526", "name": "customer_id", "type": "string", "value": "={{ $json.id }}"}, {"id": "bf288953-4fef-4f55-a45f-c223714919c0", "name": "customer_name", "type": "string", "value": "={{ $json.name }}"}, {"id": "1cff0b21-6740-4697-9d2c-9bcb045af0be", "name": "customer_description", "type": "string", "value": "={{ $json.notes }}"}]}}, "typeVersion": 3.3}], "pinData": {}, "connections": {"Edit Fields1": {"main": [[]]}, "Customer Datastore": {"main": [[{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "When clicking \"Test Workflow\"": {"main": [[{"node": "Customer Datastore", "type": "main", "index": 0}]]}}}