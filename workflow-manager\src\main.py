#!/usr/bin/env python3
"""
Flask Web Application for AgentChef Workflow Knowledge Base

This provides a web interface for searching, browsing, and managing
the workflow knowledge base.
"""

import os
import sys
import json
from datetime import datetime
from pathlib import Path
from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS

# Add parent directory to path to import our modules
sys.path.append(str(Path(__file__).parent.parent.parent))

from database_setup import WorkflowKnowledgeBase
from incremental_updater import IncrementalUpdater

app = Flask(__name__)
CORS(app)

# Initialize knowledge base
kb = WorkflowKnowledgeBase(
    db_path=str(Path(__file__).parent / "workflows.db"),
    vector_path=str(Path(__file__).parent / "vector_store.pkl")
)

@app.route('/')
def index():
    """Serve the main interface"""
    return send_from_directory('static', 'index.html')

@app.route('/api/health')
def health_check():
    """Health check endpoint"""
    try:
        stats = kb.db.get_stats()
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'database_stats': stats
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/workflows/search')
def search_workflows():
    """Search workflows endpoint"""
    try:
        query = request.args.get('q', '')
        category = request.args.get('category')
        is_free = request.args.get('is_free')
        use_semantic = request.args.get('semantic', 'true').lower() == 'true'
        limit = int(request.args.get('limit', 20))
        
        # Convert is_free parameter
        if is_free is not None:
            is_free = is_free.lower() == 'true'
        
        if query and use_semantic:
            # Use semantic search
            results = kb.search(query, use_semantic=True, limit=limit)
        else:
            # Use database search with filters
            results = kb.db.search_workflows(
                query=query if query else None,
                category=category,
                is_free=is_free,
                limit=limit
            )
        
        return jsonify({
            'status': 'success',
            'results': results,
            'count': len(results),
            'query': query,
            'filters': {
                'category': category,
                'is_free': is_free,
                'semantic': use_semantic
            }
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@app.route('/api/workflows/<workflow_id>')
def get_workflow(workflow_id):
    """Get specific workflow details"""
    try:
        workflow = kb.db.get_workflow(workflow_id)
        if workflow:
            return jsonify({
                'status': 'success',
                'workflow': workflow
            })
        else:
            return jsonify({
                'status': 'error',
                'error': 'Workflow not found'
            }), 404
            
    except Exception as e:
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@app.route('/api/workflows/<workflow_id>/recommendations')
def get_recommendations(workflow_id):
    """Get workflow recommendations"""
    try:
        limit = int(request.args.get('limit', 5))
        recommendations = kb.get_recommendations(workflow_id, limit=limit)
        
        return jsonify({
            'status': 'success',
            'recommendations': recommendations,
            'count': len(recommendations)
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@app.route('/api/stats')
def get_stats():
    """Get database statistics"""
    try:
        stats = kb.db.get_stats()
        
        # Add vector store stats
        stats['vector_store_size'] = len(kb.vector_store.embeddings)
        
        return jsonify({
            'status': 'success',
            'stats': stats
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@app.route('/api/categories')
def get_categories():
    """Get available categories"""
    try:
        stats = kb.db.get_stats()
        categories = list(stats.get('categories', {}).keys())
        
        return jsonify({
            'status': 'success',
            'categories': categories
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@app.route('/api/update/trigger', methods=['POST'])
def trigger_update():
    """Trigger manual update"""
    try:
        data = request.get_json() or {}
        max_workflows = data.get('max_workflows', 50)
        
        # Run incremental update
        updater = IncrementalUpdater(
            kb_path=str(Path(__file__).parent / "workflows.db")
        )
        
        result = updater.run_incremental_update(max_workflows=max_workflows)
        
        return jsonify({
            'status': 'success',
            'update_result': result
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@app.route('/api/update/status')
def get_update_status():
    """Get update status and history"""
    try:
        stats_file = Path(__file__).parent.parent.parent / "update_stats.json"
        sync_file = Path(__file__).parent.parent.parent / "last_sync.json"
        
        status = {
            'last_update': None,
            'update_history': [],
            'sync_info': {}
        }
        
        # Load update stats
        if stats_file.exists():
            with open(stats_file, 'r') as f:
                update_history = json.load(f)
                status['update_history'] = update_history[-10:]  # Last 10 updates
                if update_history:
                    status['last_update'] = update_history[-1]
        
        # Load sync info
        if sync_file.exists():
            with open(sync_file, 'r') as f:
                status['sync_info'] = json.load(f)
        
        return jsonify({
            'status': 'success',
            'update_status': status
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

if __name__ == '__main__':
    # Ensure database exists
    if not Path(kb.db.db_path).exists():
        print("Database not found. Please run the extractor and database setup first.")
        print("Run: python3 ../../agentchef_extractor.py")
        print("Then: python3 ../../database_setup.py")
        sys.exit(1)
    
    print("🚀 Starting AgentChef Workflow Knowledge Base Web Interface")
    print("📊 Access the interface at: http://localhost:5000")
    print("🔍 API endpoints available at: http://localhost:5000/api/")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
