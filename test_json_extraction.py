#!/usr/bin/env python3
"""
Test JSON extraction with different authentication methods
"""

import time
import json
from pathlib import Path
from agentchef_extractor import AgentChefExtractor

def test_single_workflow_with_auth():
    """Test JSON extraction on a single workflow with authentication"""
    print("🧪 Testing JSON extraction with authentication")
    print("=" * 50)
    
    # Use a known workflow ID from our previous extraction
    test_workflow_id = "ab2d1ec2-713b-4559-99d5-5aa8b4d4ef71"
    test_dir = "test_json_extraction"
    
    print(f"🎯 Target workflow: {test_workflow_id}")
    print(f"📁 Test directory: {test_dir}")
    print("🔐 Authentication: Interactive mode")
    print()
    
    try:
        # Initialize extractor with visible browser
        extractor = AgentChefExtractor(download_dir=test_dir)
        
        # Setup selenium with visible browser
        extractor.setup_selenium(headless=False)  # Visible browser
        
        # Attempt authentication
        print("🔐 Starting authentication process...")
        auth_success = extractor.authenticate(interactive=True)
        
        if not auth_success:
            print("❌ Authentication failed")
            return False
        
        print("✅ Authentication successful!")
        
        # Navigate directly to the test workflow
        workflow_url = f"{extractor.base_url}/workflow/{test_workflow_id}"
        print(f"🌐 Navigating to: {workflow_url}")
        extractor.driver.get(workflow_url)
        time.sleep(3)
        
        # Extract metadata
        print("📋 Extracting metadata...")
        metadata = extractor.extract_workflow_metadata(test_workflow_id)
        
        if metadata:
            print(f"✅ Metadata extracted: {metadata.title}")
            print(f"   Description: {metadata.description[:100]}...")
            print(f"   Is Free: {metadata.is_free}")
            
            # Attempt JSON extraction
            print("📄 Attempting JSON extraction...")
            json_data = extractor.attempt_json_extraction(test_workflow_id, metadata)
            
            if json_data:
                print("✅ JSON extraction successful!")
                print(f"   JSON keys: {list(json_data.keys())}")
                
                # Save and validate
                json_file = Path(test_dir) / "json_files" / f"{test_workflow_id}.json"
                if json_file.exists():
                    with open(json_file, 'r') as f:
                        saved_data = json.load(f)
                    print(f"✅ JSON file saved: {json_file}")
                    print(f"   File size: {json_file.stat().st_size} bytes")
                    return True
                else:
                    print("⚠️ JSON extracted but file not saved")
                    return False
            else:
                print("❌ JSON extraction failed")
                
                # Debug: Check what buttons are available
                print("🔍 Debugging: Looking for available buttons...")
                buttons = extractor.driver.find_elements("xpath", "//button")
                for i, button in enumerate(buttons[:10]):  # Show first 10 buttons
                    try:
                        text = button.text.strip()
                        if text:
                            print(f"   Button {i+1}: '{text}'")
                    except:
                        pass
                
                return False
        else:
            print("❌ Metadata extraction failed")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False
    finally:
        if extractor.driver:
            extractor.close_selenium()

def test_multiple_extraction_strategies():
    """Test different JSON extraction strategies"""
    print("\n🧪 Testing Multiple JSON Extraction Strategies")
    print("=" * 50)
    
    test_workflow_id = "ab2d1ec2-713b-4559-99d5-5aa8b4d4ef71"
    test_dir = "test_strategies"
    
    try:
        extractor = AgentChefExtractor(download_dir=test_dir)
        extractor.setup_selenium(headless=False)
        
        # Authenticate
        auth_success = extractor.authenticate(interactive=True)
        if not auth_success:
            print("❌ Authentication failed")
            return
        
        # Navigate to workflow
        workflow_url = f"{extractor.base_url}/workflow/{test_workflow_id}"
        extractor.driver.get(workflow_url)
        time.sleep(3)
        
        # Test each strategy individually
        strategies = [
            ("Download Buttons", extractor._try_download_buttons),
            ("Copy Functionality", extractor._try_copy_functionality),
            ("API Access", extractor._try_api_access),
            ("Page Source", extractor._try_page_source_extraction)
        ]
        
        for strategy_name, strategy_func in strategies:
            print(f"\n🔍 Testing: {strategy_name}")
            try:
                result = strategy_func(test_workflow_id)
                if result:
                    print(f"✅ {strategy_name} successful!")
                    print(f"   JSON keys: {list(result.keys())}")
                    break
                else:
                    print(f"❌ {strategy_name} failed")
            except Exception as e:
                print(f"❌ {strategy_name} error: {e}")
        
    except Exception as e:
        print(f"❌ Strategy test failed: {e}")
    finally:
        if extractor.driver:
            extractor.close_selenium()

def test_page_inspection():
    """Inspect the workflow page to understand the structure"""
    print("\n🧪 Inspecting Workflow Page Structure")
    print("=" * 50)
    
    test_workflow_id = "ab2d1ec2-713b-4559-99d5-5aa8b4d4ef71"
    
    try:
        extractor = AgentChefExtractor()
        extractor.setup_selenium(headless=False)
        
        # Authenticate
        auth_success = extractor.authenticate(interactive=True)
        if not auth_success:
            print("❌ Authentication failed")
            return
        
        # Navigate to workflow
        workflow_url = f"{extractor.base_url}/workflow/{test_workflow_id}"
        extractor.driver.get(workflow_url)
        time.sleep(5)
        
        print("🔍 Page inspection results:")
        
        # Check for buttons
        buttons = extractor.driver.find_elements("xpath", "//button")
        print(f"   Total buttons found: {len(buttons)}")
        
        copy_buttons = []
        download_buttons = []
        
        for button in buttons:
            try:
                text = button.text.strip().lower()
                if 'copy' in text:
                    copy_buttons.append(button.text.strip())
                elif 'download' in text or 'export' in text:
                    download_buttons.append(button.text.strip())
            except:
                pass
        
        print(f"   Copy-related buttons: {copy_buttons}")
        print(f"   Download-related buttons: {download_buttons}")
        
        # Check for links
        links = extractor.driver.find_elements("xpath", "//a")
        json_links = []
        for link in links:
            try:
                href = link.get_attribute('href')
                text = link.text.strip().lower()
                if href and ('json' in href or 'download' in href or 'export' in href):
                    json_links.append(f"{text} -> {href}")
                elif 'json' in text or 'download' in text or 'export' in text:
                    json_links.append(f"{text} -> {href}")
            except:
                pass
        
        print(f"   JSON-related links: {json_links}")
        
        # Check page source for JSON patterns
        page_source = extractor.driver.page_source
        json_indicators = [
            'workflow-json',
            'workflowData',
            'Copy Workflow JSON',
            'download',
            'export'
        ]
        
        found_indicators = []
        for indicator in json_indicators:
            if indicator.lower() in page_source.lower():
                found_indicators.append(indicator)
        
        print(f"   JSON indicators in page: {found_indicators}")
        
        # Wait for user to manually inspect
        print("\n👀 Manual inspection time!")
        print("The browser window is open - you can manually inspect the page")
        print("Try to manually copy the JSON and see what happens")
        input("Press Enter when done inspecting...")
        
    except Exception as e:
        print(f"❌ Page inspection failed: {e}")
    finally:
        if extractor.driver:
            extractor.close_selenium()

def main():
    """Run JSON extraction tests"""
    print("🧪 AgentChef JSON Extraction Testing Suite")
    print("=" * 60)
    print()
    print("This will test different approaches to extract JSON files")
    print("with proper authentication.")
    print()
    
    tests = [
        ("Single Workflow Test", test_single_workflow_with_auth),
        ("Multiple Strategies Test", test_multiple_extraction_strategies),
        ("Page Inspection", test_page_inspection)
    ]
    
    for i, (test_name, test_func) in enumerate(tests, 1):
        print(f"{i}. {test_name}")
    
    print("0. Run all tests")
    print()
    
    choice = input("Select test to run (0-3): ").strip()
    
    if choice == "0":
        for test_name, test_func in tests:
            print(f"\n🚀 Running: {test_name}")
            test_func()
    elif choice in ["1", "2", "3"]:
        test_name, test_func = tests[int(choice) - 1]
        print(f"\n🚀 Running: {test_name}")
        test_func()
    else:
        print("Invalid choice")

if __name__ == "__main__":
    main()
