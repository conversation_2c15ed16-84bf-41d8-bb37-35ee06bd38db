#!/usr/bin/env python3
"""
AgentChef Workflow Extractor

This script extracts workflow data from the AgentChef webapp at agentchef.vercel.app
and stores it in a local database for building a knowledge base.
"""

import requests
import json
import time
import re
import hashlib
import os
import shutil
from datetime import datetime
from typing import List, Dict, Optional
from urllib.parse import urljoin, urlparse
from dataclasses import dataclass
from pathlib import Path
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException, ElementClickInterceptedException
from webdriver_manager.chrome import ChromeDriverManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('agentchef_extraction.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class WorkflowMetadata:
    """Data class for workflow metadata"""
    id: str
    title: str
    description: str
    category: str
    author: str
    author_verified: bool
    view_count: int
    node_count: int
    is_free: bool
    created_date: Optional[str]
    node_types: List[str]
    url: str
    workflow_json: Optional[Dict] = None

class AgentChefExtractor:
    """Main extractor class for AgentChef workflows"""

    def __init__(self, base_url: str = "https://agentchef.vercel.app", download_dir: str = "downloaded_workflows"):
        self.base_url = base_url
        self.download_dir = Path(download_dir)
        self.download_dir.mkdir(exist_ok=True)

        # Create subdirectories for organization
        (self.download_dir / "json_files").mkdir(exist_ok=True)
        (self.download_dir / "metadata").mkdir(exist_ok=True)
        (self.download_dir / "logs").mkdir(exist_ok=True)

        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.driver = None
        self.workflows = []
        self.is_authenticated = False
        self.download_stats = {
            'total_processed': 0,
            'json_downloaded': 0,
            'metadata_only': 0,
            'paid_skipped': 0,
            'errors': 0
        }
        
    def setup_selenium(self, headless: bool = True):
        """Setup Selenium WebDriver with download capabilities"""
        chrome_options = Options()

        # Configure download settings
        download_path = str(self.download_dir.absolute())
        prefs = {
            "download.default_directory": download_path,
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True,
            "profile.default_content_settings.popups": 0,
            "profile.default_content_setting_values.automatic_downloads": 1
        }
        chrome_options.add_experimental_option("prefs", prefs)

        if headless:
            chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--allow-running-insecure-content')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            # Enable downloads in headless mode
            if headless:
                self.driver.command_executor._commands["send_command"] = ("POST", '/session/$sessionId/chromium/send_command')
                params = {'cmd': 'Page.setDownloadBehavior', 'params': {'behavior': 'allow', 'downloadPath': download_path}}
                self.driver.execute("send_command", params)

            logger.info("Selenium WebDriver initialized successfully with download capabilities")
        except Exception as e:
            logger.error(f"Failed to initialize Selenium WebDriver: {e}")
            raise
    
    def close_selenium(self):
        """Close Selenium WebDriver"""
        if self.driver:
            self.driver.quit()
            logger.info("Selenium WebDriver closed")

    def authenticate(self, email: str = None, password: str = None, interactive: bool = True):
        """Enhanced authentication with AgentChef"""
        if not self.driver:
            self.setup_selenium(headless=not interactive)

        try:
            # First, try to access a workflow page to see if auth is needed
            test_workflow_url = f"{self.base_url}/workflow/ab2d1ec2-713b-4559-99d5-5aa8b4d4ef71"
            logger.info("Testing if authentication is required...")
            self.driver.get(test_workflow_url)
            time.sleep(3)

            # Check if we're redirected to login or if we can access the page
            current_url = self.driver.current_url
            if "login" in current_url.lower() or "auth" in current_url.lower():
                logger.info("Authentication required - redirected to login page")
                need_auth = True
            else:
                logger.info("No authentication required or already logged in")
                self.is_authenticated = True
                return True

            if need_auth:
                if interactive and not email:
                    logger.info("=== INTERACTIVE AUTHENTICATION REQUIRED ===")
                    logger.info("Please log in manually in the browser window that opened")
                    logger.info("After logging in successfully, press Enter to continue...")
                    input("Waiting for manual login...")

                    # Verify authentication worked
                    self.driver.get(test_workflow_url)
                    time.sleep(2)
                    current_url = self.driver.current_url
                    if "login" not in current_url.lower():
                        self.is_authenticated = True
                        logger.info("✅ Authentication successful!")
                        return True
                    else:
                        logger.error("❌ Authentication failed - still redirected to login")
                        return False

                elif email and password:
                    logger.info("Attempting automatic authentication...")
                    return self._attempt_automatic_login(email, password)
                else:
                    logger.warning("Authentication required but no credentials provided")
                    return False

        except Exception as e:
            logger.error(f"Authentication error: {e}")
            return False

        return self.is_authenticated

    def _attempt_automatic_login(self, email: str, password: str) -> bool:
        """Attempt automatic login with credentials"""
        try:
            # Navigate to login page
            login_url = f"{self.base_url}/login"
            logger.info(f"Navigating to login page: {login_url}")
            self.driver.get(login_url)

            # Wait for page to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            # Try multiple selectors for email field
            email_selectors = [
                'input[type="email"]',
                'input[name="email"]',
                'input[placeholder*="email" i]',
                'input[id*="email" i]',
                '#email',
                '.email'
            ]

            email_field = None
            for selector in email_selectors:
                try:
                    email_field = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except NoSuchElementException:
                    continue

            if not email_field:
                logger.error("Could not find email input field")
                return False

            # Try multiple selectors for password field
            password_selectors = [
                'input[type="password"]',
                'input[name="password"]',
                'input[id*="password" i]',
                '#password',
                '.password'
            ]

            password_field = None
            for selector in password_selectors:
                try:
                    password_field = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except NoSuchElementException:
                    continue

            if not password_field:
                logger.error("Could not find password input field")
                return False

            # Fill in credentials
            logger.info("Filling in login credentials...")
            email_field.clear()
            email_field.send_keys(email)
            password_field.clear()
            password_field.send_keys(password)

            # Find and click login button
            login_selectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                'button:contains("Login")',
                'button:contains("Sign in")',
                'button:contains("Log in")',
                '.login-button',
                '.submit-button'
            ]

            login_button = None
            for selector in login_selectors:
                try:
                    login_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except NoSuchElementException:
                    continue

            if not login_button:
                logger.error("Could not find login button")
                return False

            logger.info("Clicking login button...")
            login_button.click()

            # Wait for login to process
            time.sleep(5)

            # Check if login was successful
            current_url = self.driver.current_url
            if "login" not in current_url.lower() and "auth" not in current_url.lower():
                self.is_authenticated = True
                logger.info("✅ Automatic authentication successful!")
                return True
            else:
                logger.error("❌ Automatic authentication failed")
                return False

        except Exception as e:
            logger.error(f"Error during automatic login: {e}")
            return False
    
    def extract_workflow_ids_from_browse(self, page_url: str = None) -> List[str]:
        """Extract workflow IDs from the browse page"""
        if not page_url:
            page_url = f"{self.base_url}/browse"
        
        if not self.driver:
            self.setup_selenium()
        
        workflow_ids = []
        
        try:
            logger.info(f"Loading browse page: {page_url}")
            self.driver.get(page_url)
            
            # Wait for page to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Scroll to load more content
            last_height = self.driver.execute_script("return document.body.scrollHeight")
            scroll_attempts = 0
            max_scrolls = 10  # Limit scrolling to avoid infinite loops
            
            while scroll_attempts < max_scrolls:
                # Scroll down
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(2)  # Wait for content to load
                
                # Check if new content loaded
                new_height = self.driver.execute_script("return document.body.scrollHeight")
                if new_height == last_height:
                    break
                    
                last_height = new_height
                scroll_attempts += 1
                logger.info(f"Scrolled {scroll_attempts} times, page height: {new_height}")
            
            # Extract workflow links
            workflow_links = self.driver.find_elements(By.CSS_SELECTOR, 'a[href*="/workflow/"]')
            
            for link in workflow_links:
                href = link.get_attribute('href')
                if href and '/workflow/' in href:
                    workflow_id = href.split('/workflow/')[-1]
                    if workflow_id and workflow_id not in workflow_ids:
                        workflow_ids.append(workflow_id)
            
            logger.info(f"Found {len(workflow_ids)} unique workflow IDs")
            
        except Exception as e:
            logger.error(f"Error extracting workflow IDs: {e}")
        
        return workflow_ids

    def extract_workflow_metadata(self, workflow_id: str) -> Optional[WorkflowMetadata]:
        """Extract metadata for a specific workflow"""
        workflow_url = f"{self.base_url}/workflow/{workflow_id}"

        if not self.driver:
            self.setup_selenium()

        try:
            logger.info(f"Extracting metadata for workflow: {workflow_id}")
            self.driver.get(workflow_url)

            # Wait for page to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "h1"))
            )

            # Extract title
            title_element = self.driver.find_element(By.TAG_NAME, "h1")
            title = title_element.text.strip()

            # Extract description
            description = ""
            try:
                # Look for description in various possible locations
                desc_selectors = [
                    'p:first-of-type',
                    '[class*="description"]',
                    '[class*="summary"]'
                ]
                for selector in desc_selectors:
                    try:
                        desc_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                        description = desc_element.text.strip()
                        if description:
                            break
                    except NoSuchElementException:
                        continue
            except Exception as e:
                logger.warning(f"Could not extract description for {workflow_id}: {e}")

            # Extract other metadata
            metadata = WorkflowMetadata(
                id=workflow_id,
                title=title,
                description=description,
                category="",  # Will be extracted from page
                author="",    # Will be extracted from page
                author_verified=False,
                view_count=0,
                node_count=0,
                is_free=True,
                created_date=None,
                node_types=[],
                url=workflow_url
            )

            # Try to extract additional metadata from the page
            self._extract_additional_metadata(metadata)

            # Attempt to extract JSON workflow data
            json_data = self.attempt_json_extraction(workflow_id, metadata)
            if json_data:
                metadata.workflow_json = json_data
                logger.info(f"Successfully extracted JSON for workflow {workflow_id}")
            else:
                logger.info(f"No JSON extracted for workflow {workflow_id} (metadata only)")
                self.download_stats['metadata_only'] += 1

            return metadata

        except Exception as e:
            logger.error(f"Error extracting metadata for workflow {workflow_id}: {e}")
            return None

    def _extract_additional_metadata(self, metadata: WorkflowMetadata):
        """Extract additional metadata from the current page"""
        try:
            # Extract view count
            view_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'views')]")
            for element in view_elements:
                text = element.text
                view_match = re.search(r'([\d.]+[kK]?)\s*views', text)
                if view_match:
                    view_str = view_match.group(1)
                    if 'k' in view_str.lower():
                        metadata.view_count = int(float(view_str.lower().replace('k', '')) * 1000)
                    else:
                        metadata.view_count = int(view_str)
                    break

            # Extract node count
            node_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'nodes')]")
            for element in node_elements:
                text = element.text
                node_match = re.search(r'(\d+)\s*nodes', text)
                if node_match:
                    metadata.node_count = int(node_match.group(1))
                    break

            # Extract category
            category_elements = self.driver.find_elements(By.CSS_SELECTOR, '[class*="category"], [class*="tag"]')
            for element in category_elements:
                text = element.text.strip()
                if text and len(text) < 50:  # Reasonable category length
                    metadata.category = text
                    break

            # Extract author
            author_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'by ')]")
            for element in author_elements:
                text = element.text
                author_match = re.search(r'by\s+(.+)', text)
                if author_match:
                    metadata.author = author_match.group(1).strip()
                    break

            # Check if free
            free_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'Free')]")
            metadata.is_free = len(free_elements) > 0

        except Exception as e:
            logger.warning(f"Error extracting additional metadata: {e}")

    def attempt_json_extraction(self, workflow_id: str, metadata: WorkflowMetadata) -> Optional[Dict]:
        """Comprehensive JSON workflow extraction with multiple strategies"""
        if not self.driver:
            return None

        # Skip paid workflows
        if not metadata.is_free:
            logger.info(f"Skipping paid workflow: {workflow_id} - {metadata.title}")
            self.download_stats['paid_skipped'] += 1
            return None

        logger.info(f"Attempting JSON extraction for workflow: {workflow_id}")

        try:
            # Strategy 1: Look for direct download buttons
            json_data = self._try_download_buttons(workflow_id)
            if json_data:
                return json_data

            # Strategy 2: Look for copy/export functionality
            json_data = self._try_copy_functionality(workflow_id)
            if json_data:
                return json_data

            # Strategy 3: Try to access API endpoints directly
            json_data = self._try_api_access(workflow_id)
            if json_data:
                return json_data

            # Strategy 4: Look for embedded JSON in page source
            json_data = self._try_page_source_extraction(workflow_id)
            if json_data:
                return json_data

            logger.warning(f"Could not extract JSON for workflow {workflow_id}")
            return None

        except Exception as e:
            logger.error(f"Error during JSON extraction for {workflow_id}: {e}")
            self.download_stats['errors'] += 1
            return None

    def _try_download_buttons(self, workflow_id: str) -> Optional[Dict]:
        """Try to find and click download buttons"""
        try:
            # Common selectors for download buttons
            download_selectors = [
                "//button[contains(text(), 'Download')]",
                "//button[contains(text(), 'Export')]",
                "//button[contains(text(), 'JSON')]",
                "//a[contains(text(), 'Download')]",
                "//a[contains(@href, 'download')]",
                "//a[contains(@href, 'export')]",
                "//*[@title='Download']",
                "//*[@title='Export']",
                "//button[contains(@class, 'download')]",
                "//button[contains(@class, 'export')]"
            ]

            for selector in download_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            logger.info(f"Found download button: {element.text}")

                            # Clear any existing downloads
                            self._clear_download_folder()

                            # Click the button
                            self.driver.execute_script("arguments[0].click();", element)
                            time.sleep(2)

                            # Check for downloaded file
                            json_data = self._check_for_downloaded_file(workflow_id)
                            if json_data:
                                return json_data

                except Exception as e:
                    logger.debug(f"Download button attempt failed: {e}")
                    continue

            return None

        except Exception as e:
            logger.warning(f"Error trying download buttons: {e}")
            return None

    def _try_copy_functionality(self, workflow_id: str) -> Optional[Dict]:
        """Enhanced copy functionality with multiple approaches"""
        try:
            # Look for copy buttons with more specific selectors
            copy_selectors = [
                "//button[contains(text(), 'Copy Workflow JSON')]",
                "//button[contains(text(), 'Copy JSON')]",
                "//button[contains(text(), 'Copy')]",
                "//button[contains(@title, 'Copy')]",
                "//*[contains(@class, 'copy')]",
                "//button[contains(text(), 'Copy Workflow')]",
                "//a[contains(text(), 'Copy')]",
                "//*[@data-action='copy']"
            ]

            for selector in copy_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            logger.info(f"Found copy button: {element.text}")

                            # Try multiple click approaches
                            success = self._try_multiple_click_methods(element)
                            if not success:
                                continue

                            # Try multiple clipboard extraction methods
                            json_data = self._try_multiple_clipboard_methods(workflow_id)
                            if json_data:
                                return json_data

                except Exception as e:
                    logger.debug(f"Copy button attempt failed: {e}")
                    continue

            return None

        except Exception as e:
            logger.warning(f"Error trying copy functionality: {e}")
            return None

    def _try_multiple_click_methods(self, element) -> bool:
        """Try multiple ways to click an element"""
        try:
            # Method 1: Regular click
            try:
                element.click()
                time.sleep(1)
                return True
            except:
                pass

            # Method 2: JavaScript click
            try:
                self.driver.execute_script("arguments[0].click();", element)
                time.sleep(1)
                return True
            except:
                pass

            # Method 3: Action chains
            try:
                actions = ActionChains(self.driver)
                actions.move_to_element(element).click().perform()
                time.sleep(1)
                return True
            except:
                pass

            # Method 4: Force focus and click
            try:
                self.driver.execute_script("arguments[0].focus(); arguments[0].click();", element)
                time.sleep(1)
                return True
            except:
                pass

            return False

        except Exception as e:
            logger.debug(f"All click methods failed: {e}")
            return False

    def _try_multiple_clipboard_methods(self, workflow_id: str) -> Optional[Dict]:
        """Try multiple methods to extract clipboard content"""

        # Method 1: Textarea paste method
        try:
            json_data = self._try_textarea_paste_method(workflow_id)
            if json_data:
                return json_data
        except Exception as e:
            logger.debug(f"Textarea paste method failed: {e}")

        # Method 2: Direct clipboard API (if available)
        try:
            json_data = self._try_clipboard_api_method(workflow_id)
            if json_data:
                return json_data
        except Exception as e:
            logger.debug(f"Clipboard API method failed: {e}")

        # Method 3: Check for JSON in page source after copy
        try:
            json_data = self._try_page_source_after_copy(workflow_id)
            if json_data:
                return json_data
        except Exception as e:
            logger.debug(f"Page source after copy method failed: {e}")

        return None

    def _try_textarea_paste_method(self, workflow_id: str) -> Optional[Dict]:
        """Try to paste clipboard content into a textarea"""
        try:
            # Create a temporary textarea to paste content
            self.driver.execute_script("""
                var textarea = document.createElement('textarea');
                textarea.id = 'temp-clipboard-' + Date.now();
                textarea.style.position = 'fixed';
                textarea.style.top = '0';
                textarea.style.left = '0';
                textarea.style.width = '1px';
                textarea.style.height = '1px';
                textarea.style.opacity = '0';
                document.body.appendChild(textarea);
                textarea.focus();
                textarea.select();
            """)

            time.sleep(0.5)

            # Find the textarea and paste
            textareas = self.driver.find_elements(By.CSS_SELECTOR, "textarea[id^='temp-clipboard-']")
            if textareas:
                textarea = textareas[0]
                textarea.send_keys(Keys.CONTROL + "v")
                time.sleep(1)

                clipboard_content = textarea.get_attribute("value")

                # Clean up
                textarea_id = textarea.get_attribute("id")
                self.driver.execute_script(f"document.getElementById('{textarea_id}').remove();")

                if clipboard_content and clipboard_content.strip():
                    # Try to parse as JSON
                    try:
                        json_data = json.loads(clipboard_content)
                        logger.info(f"✅ Successfully extracted JSON via textarea paste for {workflow_id}")
                        self._save_json_file(workflow_id, json_data)
                        return json_data
                    except json.JSONDecodeError:
                        logger.debug(f"Clipboard content is not valid JSON: {clipboard_content[:100]}...")

            return None

        except Exception as e:
            logger.debug(f"Textarea paste method error: {e}")
            return None

    def _try_clipboard_api_method(self, workflow_id: str) -> Optional[Dict]:
        """Try to use the Clipboard API directly"""
        try:
            # Try to read clipboard using JavaScript
            clipboard_content = self.driver.execute_script("""
                return navigator.clipboard.readText().then(text => {
                    return text;
                }).catch(err => {
                    return null;
                });
            """)

            if clipboard_content and clipboard_content.strip():
                try:
                    json_data = json.loads(clipboard_content)
                    logger.info(f"✅ Successfully extracted JSON via Clipboard API for {workflow_id}")
                    self._save_json_file(workflow_id, json_data)
                    return json_data
                except json.JSONDecodeError:
                    logger.debug("Clipboard API content is not valid JSON")

            return None

        except Exception as e:
            logger.debug(f"Clipboard API method error: {e}")
            return None

    def _try_page_source_after_copy(self, workflow_id: str) -> Optional[Dict]:
        """Check if JSON appears in page source after copy action"""
        try:
            # Wait a moment for any dynamic content to load
            time.sleep(2)

            page_source = self.driver.page_source

            # Look for JSON patterns that might have been injected
            patterns = [
                r'data-clipboard-text="({.*?})"',
                r'clipboard-content["\']:\s*["\']({.*?})["\']',
                r'workflow-json["\']:\s*({.*?})',
                r'"workflowData":\s*({.*?})'
            ]

            for pattern in patterns:
                matches = re.findall(pattern, page_source, re.DOTALL)
                for match in matches:
                    try:
                        # Unescape and parse JSON
                        json_str = match.replace('&quot;', '"').replace('&#x27;', "'")
                        json_data = json.loads(json_str)

                        if self._validate_workflow_json(json_data):
                            logger.info(f"✅ Successfully extracted JSON from page source after copy for {workflow_id}")
                            self._save_json_file(workflow_id, json_data)
                            return json_data

                    except json.JSONDecodeError:
                        continue

            return None

        except Exception as e:
            logger.debug(f"Page source after copy method error: {e}")
            return None

    def _try_api_access(self, workflow_id: str) -> Optional[Dict]:
        """Try to access workflow data via API endpoints"""
        try:
            # Common API endpoint patterns
            api_endpoints = [
                f"{self.base_url}/api/workflow/{workflow_id}",
                f"{self.base_url}/api/workflows/{workflow_id}",
                f"{self.base_url}/api/v1/workflow/{workflow_id}",
                f"{self.base_url}/api/v1/workflows/{workflow_id}",
                f"{self.base_url}/workflow/{workflow_id}/json",
                f"{self.base_url}/workflow/{workflow_id}/export",
                f"{self.base_url}/workflow/{workflow_id}/download"
            ]

            # Get cookies from current session
            cookies = self.driver.get_cookies()
            session_cookies = {cookie['name']: cookie['value'] for cookie in cookies}

            for endpoint in api_endpoints:
                try:
                    response = self.session.get(endpoint, cookies=session_cookies, timeout=10)
                    if response.status_code == 200:
                        try:
                            json_data = response.json()
                            logger.info(f"Successfully extracted JSON via API: {endpoint}")
                            self._save_json_file(workflow_id, json_data)
                            return json_data
                        except json.JSONDecodeError:
                            logger.debug(f"API response is not JSON: {endpoint}")
                except Exception as e:
                    logger.debug(f"API endpoint failed {endpoint}: {e}")
                    continue

            return None

        except Exception as e:
            logger.warning(f"Error trying API access: {e}")
            return None

    def _try_page_source_extraction(self, workflow_id: str) -> Optional[Dict]:
        """Try to extract JSON from page source"""
        try:
            page_source = self.driver.page_source

            # Look for JSON data in script tags or data attributes
            patterns = [
                r'window\.__INITIAL_STATE__\s*=\s*({.*?});',
                r'window\.__WORKFLOW_DATA__\s*=\s*({.*?});',
                r'data-workflow\s*=\s*["\']({.*?})["\']',
                r'"workflow"\s*:\s*({.*?})',
                r'workflowData\s*=\s*({.*?});'
            ]

            for pattern in patterns:
                matches = re.findall(pattern, page_source, re.DOTALL)
                for match in matches:
                    try:
                        # Clean up the match
                        json_str = match.strip()
                        if json_str.endswith(','):
                            json_str = json_str[:-1]

                        json_data = json.loads(json_str)

                        # Validate that this looks like workflow data
                        if self._validate_workflow_json(json_data):
                            logger.info(f"Successfully extracted JSON from page source for {workflow_id}")
                            self._save_json_file(workflow_id, json_data)
                            return json_data

                    except json.JSONDecodeError:
                        continue

            return None

        except Exception as e:
            logger.warning(f"Error trying page source extraction: {e}")
            return None

    def _validate_workflow_json(self, data: Dict) -> bool:
        """Validate that JSON data looks like a workflow"""
        if not isinstance(data, dict):
            return False

        # Look for common workflow fields in keys (not values)
        workflow_indicators = ['nodes', 'edges', 'connections', 'steps', 'workflow', 'flow']
        data_keys = [key.lower() for key in data.keys()]
        return any(indicator in data_keys for indicator in workflow_indicators)

    def _clear_download_folder(self):
        """Clear the download folder of any existing files"""
        try:
            for file in self.download_dir.glob("*"):
                if file.is_file() and not file.name.startswith('.'):
                    file.unlink()
        except Exception as e:
            logger.debug(f"Error clearing download folder: {e}")

    def _check_for_downloaded_file(self, workflow_id: str, timeout: int = 10) -> Optional[Dict]:
        """Check for downloaded files and process them"""
        try:
            start_time = time.time()
            while time.time() - start_time < timeout:
                # Look for downloaded files
                downloaded_files = list(self.download_dir.glob("*"))
                downloaded_files = [f for f in downloaded_files if f.is_file() and not f.name.startswith('.')]

                if downloaded_files:
                    # Process the most recent file
                    latest_file = max(downloaded_files, key=lambda f: f.stat().st_mtime)

                    # Check if it's a JSON file or contains JSON
                    try:
                        if latest_file.suffix.lower() == '.json':
                            with open(latest_file, 'r', encoding='utf-8') as f:
                                json_data = json.load(f)

                            # Move to proper location
                            self._save_json_file(workflow_id, json_data)
                            latest_file.unlink()  # Remove original

                            logger.info(f"Successfully processed downloaded JSON file for {workflow_id}")
                            return json_data

                        # Try to read as text and parse as JSON
                        with open(latest_file, 'r', encoding='utf-8') as f:
                            content = f.read()

                        if content.strip().startswith('{'):
                            json_data = json.loads(content)
                            self._save_json_file(workflow_id, json_data)
                            latest_file.unlink()  # Remove original

                            logger.info(f"Successfully processed downloaded file as JSON for {workflow_id}")
                            return json_data

                    except Exception as e:
                        logger.debug(f"Downloaded file is not valid JSON: {e}")
                        latest_file.unlink()  # Remove invalid file

                time.sleep(0.5)

            return None

        except Exception as e:
            logger.warning(f"Error checking for downloaded files: {e}")
            return None

    def _save_json_file(self, workflow_id: str, json_data: Dict):
        """Save JSON data to organized file structure"""
        try:
            json_file_path = self.download_dir / "json_files" / f"{workflow_id}.json"
            with open(json_file_path, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, indent=2, ensure_ascii=False)

            logger.info(f"Saved JSON file: {json_file_path}")
            self.download_stats['json_downloaded'] += 1

        except Exception as e:
            logger.error(f"Error saving JSON file for {workflow_id}: {e}")

    def save_workflow_data(self, workflows: List[WorkflowMetadata], filename: str = "workflows_data.json"):
        """Save extracted workflow data to JSON file"""
        data = []
        for workflow in workflows:
            data.append({
                'id': workflow.id,
                'title': workflow.title,
                'description': workflow.description,
                'category': workflow.category,
                'author': workflow.author,
                'author_verified': workflow.author_verified,
                'view_count': workflow.view_count,
                'node_count': workflow.node_count,
                'is_free': workflow.is_free,
                'created_date': workflow.created_date,
                'node_types': workflow.node_types,
                'url': workflow.url,
                'extracted_at': datetime.now().isoformat()
            })

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)

        logger.info(f"Saved {len(data)} workflows to {filename}")

    def run_extraction(self, max_workflows: int = 50, email: str = None, password: str = None,
                      interactive_auth: bool = True, extract_json: bool = True):
        """Run the complete extraction process with enhanced capabilities"""
        try:
            logger.info("Starting AgentChef workflow extraction with JSON downloading")
            logger.info(f"Target: {max_workflows} workflows, JSON extraction: {extract_json}")

            # Setup selenium
            self.setup_selenium(headless=not interactive_auth)

            # Attempt authentication if credentials provided or interactive mode
            if email and password:
                logger.info("Attempting automatic authentication")
                auth_success = self.authenticate(email, password, interactive=False)
                if not auth_success:
                    logger.warning("Automatic authentication failed, continuing without auth")
            elif interactive_auth:
                logger.info("Interactive authentication mode available")
                auth_success = self.authenticate(interactive=True)
                if not auth_success:
                    logger.warning("Authentication skipped, continuing without auth")

            # Extract workflow IDs
            workflow_ids = self.extract_workflow_ids_from_browse()

            if not workflow_ids:
                logger.error("No workflow IDs found")
                return

            # Limit the number of workflows
            workflow_ids = workflow_ids[:max_workflows]
            logger.info(f"Processing {len(workflow_ids)} workflows")

            # Reset stats
            self.download_stats = {
                'total_processed': 0,
                'json_downloaded': 0,
                'metadata_only': 0,
                'paid_skipped': 0,
                'errors': 0
            }

            # Extract metadata and JSON for each workflow
            extracted_workflows = []
            for i, workflow_id in enumerate(workflow_ids):
                logger.info(f"Processing workflow {i+1}/{len(workflow_ids)}: {workflow_id}")

                try:
                    metadata = self.extract_workflow_metadata(workflow_id)
                    if metadata:
                        extracted_workflows.append(metadata)
                        self.download_stats['total_processed'] += 1

                        # Save individual metadata file
                        self._save_metadata_file(workflow_id, metadata)

                except Exception as e:
                    logger.error(f"Error processing workflow {workflow_id}: {e}")
                    self.download_stats['errors'] += 1

                # Add delay to be respectful to the server
                time.sleep(2)

            # Save the extracted data
            self.save_workflow_data(extracted_workflows)

            # Save download statistics
            self._save_download_stats()

            # Print summary
            self._print_extraction_summary()

        except Exception as e:
            logger.error(f"Error during extraction: {e}")
        finally:
            self.close_selenium()

    def _save_metadata_file(self, workflow_id: str, metadata: WorkflowMetadata):
        """Save individual metadata file"""
        try:
            metadata_file = self.download_dir / "metadata" / f"{workflow_id}_metadata.json"
            metadata_dict = {
                'id': metadata.id,
                'title': metadata.title,
                'description': metadata.description,
                'category': metadata.category,
                'author': metadata.author,
                'author_verified': metadata.author_verified,
                'view_count': metadata.view_count,
                'node_count': metadata.node_count,
                'is_free': metadata.is_free,
                'created_date': metadata.created_date,
                'node_types': metadata.node_types,
                'url': metadata.url,
                'has_json': metadata.workflow_json is not None,
                'extracted_at': datetime.now().isoformat()
            }

            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata_dict, f, indent=2, ensure_ascii=False)

        except Exception as e:
            logger.error(f"Error saving metadata file for {workflow_id}: {e}")

    def _save_download_stats(self):
        """Save download statistics"""
        try:
            stats_file = self.download_dir / "logs" / f"extraction_stats_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            stats = {
                **self.download_stats,
                'extraction_date': datetime.now().isoformat(),
                'success_rate': (self.download_stats['total_processed'] / max(1, self.download_stats['total_processed'] + self.download_stats['errors'])) * 100,
                'json_success_rate': (self.download_stats['json_downloaded'] / max(1, self.download_stats['total_processed'])) * 100
            }

            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(stats, f, indent=2)

            logger.info(f"Saved extraction statistics to {stats_file}")

        except Exception as e:
            logger.error(f"Error saving download stats: {e}")

    def _print_extraction_summary(self):
        """Print extraction summary"""
        logger.info("=" * 60)
        logger.info("EXTRACTION SUMMARY")
        logger.info("=" * 60)
        logger.info(f"Total workflows processed: {self.download_stats['total_processed']}")
        logger.info(f"JSON files downloaded: {self.download_stats['json_downloaded']}")
        logger.info(f"Metadata-only extractions: {self.download_stats['metadata_only']}")
        logger.info(f"Paid workflows skipped: {self.download_stats['paid_skipped']}")
        logger.info(f"Errors encountered: {self.download_stats['errors']}")

        if self.download_stats['total_processed'] > 0:
            json_rate = (self.download_stats['json_downloaded'] / self.download_stats['total_processed']) * 100
            logger.info(f"JSON extraction success rate: {json_rate:.1f}%")

        logger.info(f"Files saved to: {self.download_dir}")
        logger.info("=" * 60)

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='AgentChef Workflow Extractor with JSON downloading')
    parser.add_argument('--max-workflows', type=int, default=10, help='Maximum number of workflows to extract')
    parser.add_argument('--email', type=str, help='Email for authentication')
    parser.add_argument('--password', type=str, help='Password for authentication')
    parser.add_argument('--no-interactive', action='store_true', help='Disable interactive authentication')
    parser.add_argument('--no-json', action='store_true', help='Skip JSON extraction, metadata only')
    parser.add_argument('--download-dir', type=str, default='downloaded_workflows', help='Directory for downloaded files')

    args = parser.parse_args()

    logger.info("🚀 Starting Enhanced AgentChef Workflow Extractor")
    logger.info(f"📁 Download directory: {args.download_dir}")
    logger.info(f"🎯 Target workflows: {args.max_workflows}")
    logger.info(f"📄 JSON extraction: {'Disabled' if args.no_json else 'Enabled'}")
    logger.info(f"🔐 Authentication: {'Disabled' if args.no_interactive else 'Interactive mode available'}")

    extractor = AgentChefExtractor(download_dir=args.download_dir)
    extractor.run_extraction(
        max_workflows=args.max_workflows,
        email=args.email,
        password=args.password,
        interactive_auth=not args.no_interactive,
        extract_json=not args.no_json
    )
