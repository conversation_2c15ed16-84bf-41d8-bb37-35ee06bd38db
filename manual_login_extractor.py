#!/usr/bin/env python3
"""
Manual Login AgentChef Extractor
Opens a visible browser for you to log in manually, then extracts workflows with JSON files
"""

import time
import json
from pathlib import Path
from agentchef_extractor import AgentChefExtractor

def main():
    print("🔐 Manual Login AgentChef Extractor")
    print("=" * 50)
    print()
    print("This script will:")
    print("1. Open a visible Chrome browser window")
    print("2. Navigate to AgentChef")
    print("3. Wait for you to log in manually")
    print("4. Extract workflows with JSON files")
    print("5. Save everything to organized folders")
    print()
    
    # Configuration
    max_workflows = 10  # Start with a reasonable number
    download_dir = "agentchef_manual_login"
    
    print(f"📋 Configuration:")
    print(f"   Target workflows: {max_workflows}")
    print(f"   Download directory: {download_dir}")
    print(f"   Browser: Visible (for manual login)")
    print(f"   JSON extraction: Enabled")
    print()
    
    input("Press Enter to start the browser and begin extraction...")
    
    try:
        print("\n🚀 Starting browser...")
        
        # Initialize extractor
        extractor = AgentChefExtractor(download_dir=download_dir)
        
        # Setup visible browser
        print("🌐 Opening Chrome browser window...")
        extractor.setup_selenium(headless=False)  # Visible browser
        
        # Navigate to AgentChef
        print("📍 Navigating to AgentChef...")
        extractor.driver.get("https://agentchef.vercel.app")
        time.sleep(3)
        
        print("\n" + "="*60)
        print("🔐 MANUAL LOGIN REQUIRED")
        print("="*60)
        print("Please log in to AgentChef in the browser window that just opened.")
        print()
        print("Steps:")
        print("1. Look for the Chrome browser window")
        print("2. Click 'Login' or 'Sign In' on the AgentChef website")
        print("3. Complete the login process")
        print("4. Once logged in, come back here and press Enter")
        print()
        print("⚠️  Do NOT close the browser window!")
        print("="*60)
        
        input("Press Enter AFTER you have successfully logged in...")
        
        print("\n✅ Proceeding with authenticated extraction...")
        
        # Mark as authenticated
        extractor.is_authenticated = True
        
        # Test authentication by trying to access a workflow
        print("🧪 Testing authentication...")
        test_url = "https://agentchef.vercel.app/workflow/ab2d1ec2-713b-4559-99d5-5aa8b4d4ef71"
        extractor.driver.get(test_url)
        time.sleep(3)
        
        current_url = extractor.driver.current_url
        if "login" in current_url.lower() or "auth" in current_url.lower():
            print("❌ Authentication test failed - still redirected to login")
            print("Please make sure you're logged in and try again")
            return
        else:
            print("✅ Authentication test passed!")
        
        # Navigate to browse page and extract workflows
        print("🔍 Loading browse page...")
        extractor.driver.get("https://agentchef.vercel.app/browse")
        time.sleep(5)
        
        # Extract workflow IDs
        workflow_ids = extractor.extract_workflow_ids_from_browse()
        
        if not workflow_ids:
            print("❌ No workflow IDs found on browse page")
            print("🔍 Let's try to debug this...")
            
            # Debug: check page content
            page_title = extractor.driver.title
            print(f"   Page title: {page_title}")
            
            # Look for workflow links manually
            links = extractor.driver.find_elements("xpath", "//a")
            workflow_links = []
            for link in links:
                href = link.get_attribute('href')
                if href and '/workflow/' in href:
                    workflow_links.append(href)
            
            print(f"   Found {len(workflow_links)} workflow links")
            if workflow_links:
                print("   Sample links:")
                for link in workflow_links[:3]:
                    print(f"     {link}")
            
            return
        
        print(f"✅ Found {len(workflow_ids)} workflow IDs")
        
        # Limit to requested number
        workflow_ids = workflow_ids[:max_workflows]
        print(f"📊 Processing {len(workflow_ids)} workflows...")
        
        # Reset stats
        extractor.download_stats = {
            'total_processed': 0,
            'json_downloaded': 0,
            'metadata_only': 0,
            'paid_skipped': 0,
            'errors': 0
        }
        
        # Extract each workflow
        extracted_workflows = []
        
        for i, workflow_id in enumerate(workflow_ids):
            print(f"\n🔄 Processing workflow {i+1}/{len(workflow_ids)}: {workflow_id}")
            
            try:
                # Extract metadata and JSON
                metadata = extractor.extract_workflow_metadata(workflow_id)
                
                if metadata:
                    extracted_workflows.append(metadata)
                    extractor.download_stats['total_processed'] += 1
                    
                    # Save individual metadata
                    extractor._save_metadata_file(workflow_id, metadata)
                    
                    print(f"   ✅ Title: {metadata.title}")
                    print(f"   📄 Description: {metadata.description[:100]}...")
                    print(f"   💰 Free: {metadata.is_free}")
                    
                    if metadata.workflow_json:
                        print(f"   📄 JSON: Successfully extracted!")
                    else:
                        print(f"   📄 JSON: Not extracted")
                
            except Exception as e:
                print(f"   ❌ Error: {e}")
                extractor.download_stats['errors'] += 1
            
            # Respectful delay
            time.sleep(2)
        
        # Save combined data
        extractor.save_workflow_data(extracted_workflows)
        extractor._save_download_stats()
        
        # Print final summary
        print("\n" + "="*60)
        print("📊 EXTRACTION SUMMARY")
        print("="*60)
        stats = extractor.download_stats
        print(f"Total workflows processed: {stats['total_processed']}")
        print(f"JSON files downloaded: {stats['json_downloaded']}")
        print(f"Metadata-only extractions: {stats['metadata_only']}")
        print(f"Paid workflows skipped: {stats['paid_skipped']}")
        print(f"Errors encountered: {stats['errors']}")
        
        if stats['total_processed'] > 0:
            json_rate = (stats['json_downloaded'] / stats['total_processed']) * 100
            print(f"JSON extraction success rate: {json_rate:.1f}%")
        
        print(f"\n📁 Files saved to: {download_dir}")
        print("="*60)
        
        # Show what files were created
        download_path = Path(download_dir)
        if download_path.exists():
            json_files = list((download_path / "json_files").glob("*.json"))
            metadata_files = list((download_path / "metadata").glob("*.json"))
            
            print(f"\n📄 Files created:")
            print(f"   JSON files: {len(json_files)}")
            print(f"   Metadata files: {len(metadata_files)}")
            print(f"   Combined data: workflows_data.json")
            
            if json_files:
                print(f"\n📄 JSON files downloaded:")
                for json_file in json_files:
                    workflow_id = json_file.stem
                    size = json_file.stat().st_size
                    print(f"   {workflow_id}.json ({size} bytes)")
        
        print("\n✅ Extraction completed successfully!")
        
        # Keep browser open for inspection
        print("\n👀 Browser window will stay open for 30 seconds for inspection...")
        print("You can manually check the extracted data or close the browser.")
        time.sleep(30)
        
    except KeyboardInterrupt:
        print("\n⚠️ Extraction interrupted by user")
    except Exception as e:
        print(f"\n❌ Extraction failed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if 'extractor' in locals() and extractor.driver:
            print("🔒 Closing browser...")
            extractor.close_selenium()

if __name__ == "__main__":
    main()
