#!/usr/bin/env python3
"""
Database Setup for AgentChef Workflow Knowledge Base

This script sets up the local database and vector store for storing
extracted workflow data and enabling semantic search.
"""

import sqlite3
import json
import hashlib
import pickle
import numpy as np
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WorkflowDatabase:
    """SQLite database for workflow metadata"""
    
    def __init__(self, db_path: str = "workflows.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize the database with required tables"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create workflows table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS workflows (
                id TEXT PRIMARY KEY,
                title TEXT NOT NULL,
                description TEXT,
                category TEXT,
                author TEXT,
                author_verified BOOLEAN DEFAULT FALSE,
                view_count INTEGER DEFAULT 0,
                node_count INTEGER DEFAULT 0,
                is_free BOOLEAN DEFAULT TRUE,
                created_date TEXT,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                workflow_json TEXT,
                url TEXT,
                status TEXT DEFAULT 'active',
                data_hash TEXT
            )
        ''')
        
        # Create node_types table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS node_types (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                description TEXT,
                category TEXT
            )
        ''')
        
        # Create workflow_node_types junction table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS workflow_node_types (
                workflow_id TEXT,
                node_type_id INTEGER,
                PRIMARY KEY (workflow_id, node_type_id),
                FOREIGN KEY (workflow_id) REFERENCES workflows(id),
                FOREIGN KEY (node_type_id) REFERENCES node_types(id)
            )
        ''')
        
        # Create categories table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                description TEXT
            )
        ''')
        
        # Create extraction_logs table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS extraction_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                workflow_id TEXT,
                extraction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status TEXT,
                error_message TEXT,
                data_hash TEXT
            )
        ''')
        
        # Create indexes for better performance
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_workflows_category ON workflows(category)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_workflows_author ON workflows(author)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_workflows_view_count ON workflows(view_count)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_workflows_is_free ON workflows(is_free)')
        
        conn.commit()
        conn.close()
        logger.info(f"Database initialized at {self.db_path}")
    
    def insert_workflow(self, workflow_data: Dict) -> bool:
        """Insert or update a workflow in the database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Calculate data hash for change detection
            data_str = json.dumps(workflow_data, sort_keys=True)
            data_hash = hashlib.md5(data_str.encode()).hexdigest()
            
            # Insert or replace workflow
            cursor.execute('''
                INSERT OR REPLACE INTO workflows (
                    id, title, description, category, author, author_verified,
                    view_count, node_count, is_free, created_date, workflow_json,
                    url, data_hash, last_updated
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                workflow_data.get('id'),
                workflow_data.get('title'),
                workflow_data.get('description'),
                workflow_data.get('category'),
                workflow_data.get('author'),
                workflow_data.get('author_verified', False),
                workflow_data.get('view_count', 0),
                workflow_data.get('node_count', 0),
                workflow_data.get('is_free', True),
                workflow_data.get('created_date'),
                json.dumps(workflow_data.get('workflow_json')) if workflow_data.get('workflow_json') else None,
                workflow_data.get('url'),
                data_hash,
                datetime.now().isoformat()
            ))
            
            # Handle node types
            node_types = workflow_data.get('node_types', [])
            if node_types:
                # Insert node types if they don't exist
                for node_type in node_types:
                    cursor.execute(
                        'INSERT OR IGNORE INTO node_types (name) VALUES (?)',
                        (node_type,)
                    )
                
                # Link workflow to node types
                cursor.execute('DELETE FROM workflow_node_types WHERE workflow_id = ?', (workflow_data.get('id'),))
                for node_type in node_types:
                    cursor.execute('''
                        INSERT INTO workflow_node_types (workflow_id, node_type_id)
                        SELECT ?, id FROM node_types WHERE name = ?
                    ''', (workflow_data.get('id'), node_type))
            
            # Log the extraction
            cursor.execute('''
                INSERT INTO extraction_logs (workflow_id, status, data_hash)
                VALUES (?, ?, ?)
            ''', (workflow_data.get('id'), 'success', data_hash))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"Error inserting workflow {workflow_data.get('id')}: {e}")
            return False
    
    def get_workflow(self, workflow_id: str) -> Optional[Dict]:
        """Get a workflow by ID"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM workflows WHERE id = ?', (workflow_id,))
        row = cursor.fetchone()
        
        if row:
            workflow = dict(row)
            # Get node types
            cursor.execute('''
                SELECT nt.name FROM node_types nt
                JOIN workflow_node_types wnt ON nt.id = wnt.node_type_id
                WHERE wnt.workflow_id = ?
            ''', (workflow_id,))
            workflow['node_types'] = [row[0] for row in cursor.fetchall()]
            
            conn.close()
            return workflow
        
        conn.close()
        return None
    
    def search_workflows(self, query: str = None, category: str = None, 
                        is_free: bool = None, limit: int = 50) -> List[Dict]:
        """Search workflows with filters"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        sql = "SELECT * FROM workflows WHERE status = 'active'"
        params = []
        
        if query:
            sql += " AND (title LIKE ? OR description LIKE ?)"
            params.extend([f"%{query}%", f"%{query}%"])
        
        if category:
            sql += " AND category = ?"
            params.append(category)
        
        if is_free is not None:
            sql += " AND is_free = ?"
            params.append(is_free)
        
        sql += " ORDER BY view_count DESC LIMIT ?"
        params.append(limit)
        
        cursor.execute(sql, params)
        workflows = [dict(row) for row in cursor.fetchall()]
        
        conn.close()
        return workflows
    
    def get_stats(self) -> Dict:
        """Get database statistics"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        stats = {}
        
        # Total workflows
        cursor.execute("SELECT COUNT(*) FROM workflows WHERE status = 'active'")
        stats['total_workflows'] = cursor.fetchone()[0]
        
        # Categories
        cursor.execute("SELECT category, COUNT(*) FROM workflows WHERE status = 'active' GROUP BY category")
        stats['categories'] = dict(cursor.fetchall())
        
        # Free vs paid
        cursor.execute("SELECT is_free, COUNT(*) FROM workflows WHERE status = 'active' GROUP BY is_free")
        free_paid = dict(cursor.fetchall())
        stats['free_workflows'] = free_paid.get(1, 0)
        stats['paid_workflows'] = free_paid.get(0, 0)
        
        # Top node types
        cursor.execute('''
            SELECT nt.name, COUNT(*) as count FROM node_types nt
            JOIN workflow_node_types wnt ON nt.id = wnt.node_type_id
            JOIN workflows w ON wnt.workflow_id = w.id
            WHERE w.status = 'active'
            GROUP BY nt.name ORDER BY count DESC LIMIT 10
        ''')
        stats['top_node_types'] = dict(cursor.fetchall())
        
        conn.close()
        return stats

class SimpleVectorStore:
    """Simple vector store for semantic search using basic text similarity"""
    
    def __init__(self, store_path: str = "vector_store.pkl"):
        self.store_path = store_path
        self.embeddings = {}
        self.metadata = {}
        self.load_store()
    
    def load_store(self):
        """Load existing vector store"""
        if Path(self.store_path).exists():
            try:
                with open(self.store_path, 'rb') as f:
                    data = pickle.load(f)
                    self.embeddings = data.get('embeddings', {})
                    self.metadata = data.get('metadata', {})
                logger.info(f"Loaded vector store with {len(self.embeddings)} embeddings")
            except Exception as e:
                logger.error(f"Error loading vector store: {e}")
    
    def save_store(self):
        """Save vector store to disk"""
        try:
            data = {
                'embeddings': self.embeddings,
                'metadata': self.metadata
            }
            with open(self.store_path, 'wb') as f:
                pickle.dump(data, f)
            logger.info(f"Saved vector store with {len(self.embeddings)} embeddings")
        except Exception as e:
            logger.error(f"Error saving vector store: {e}")
    
    def simple_text_embedding(self, text: str) -> np.ndarray:
        """Create a simple text embedding using character frequencies"""
        # This is a very basic embedding - in production, use proper embeddings
        text = text.lower()
        # Create a simple frequency vector for common characters
        chars = 'abcdefghijklmnopqrstuvwxyz0123456789 '
        vector = np.zeros(len(chars))
        
        for i, char in enumerate(chars):
            vector[i] = text.count(char) / len(text) if text else 0
        
        # Normalize
        norm = np.linalg.norm(vector)
        if norm > 0:
            vector = vector / norm
        
        return vector
    
    def add_workflow(self, workflow_id: str, title: str, description: str, metadata: Dict):
        """Add a workflow to the vector store"""
        # Combine title and description for embedding
        combined_text = f"{title} {description}"
        embedding = self.simple_text_embedding(combined_text)
        
        self.embeddings[workflow_id] = embedding
        self.metadata[workflow_id] = {
            'title': title,
            'description': description,
            **metadata
        }
    
    def search_similar(self, query: str, top_k: int = 10) -> List[Tuple[str, float]]:
        """Search for similar workflows"""
        if not self.embeddings:
            return []
        
        query_embedding = self.simple_text_embedding(query)
        similarities = []
        
        for workflow_id, embedding in self.embeddings.items():
            # Calculate cosine similarity
            similarity = np.dot(query_embedding, embedding)
            similarities.append((workflow_id, similarity))
        
        # Sort by similarity and return top_k
        similarities.sort(key=lambda x: x[1], reverse=True)
        return similarities[:top_k]

class WorkflowKnowledgeBase:
    """Main class combining database and vector store"""
    
    def __init__(self, db_path: str = "workflows.db", vector_path: str = "vector_store.pkl"):
        self.db = WorkflowDatabase(db_path)
        self.vector_store = SimpleVectorStore(vector_path)
    
    def add_workflows_from_json(self, json_file: str):
        """Add workflows from extracted JSON file"""
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                workflows = json.load(f)
            
            added_count = 0
            for workflow in workflows:
                # Add to database
                if self.db.insert_workflow(workflow):
                    # Add to vector store
                    self.vector_store.add_workflow(
                        workflow['id'],
                        workflow.get('title', ''),
                        workflow.get('description', ''),
                        {
                            'category': workflow.get('category', ''),
                            'author': workflow.get('author', ''),
                            'view_count': workflow.get('view_count', 0),
                            'is_free': workflow.get('is_free', True)
                        }
                    )
                    added_count += 1
            
            # Save vector store
            self.vector_store.save_store()
            
            logger.info(f"Added {added_count} workflows to knowledge base")
            return added_count
            
        except Exception as e:
            logger.error(f"Error adding workflows from {json_file}: {e}")
            return 0
    
    def search(self, query: str, use_semantic: bool = True, limit: int = 10) -> List[Dict]:
        """Search workflows using both database and vector search"""
        results = []
        
        if use_semantic and self.vector_store.embeddings:
            # Semantic search using vector store
            similar_ids = self.vector_store.search_similar(query, limit)
            
            for workflow_id, similarity in similar_ids:
                workflow = self.db.get_workflow(workflow_id)
                if workflow:
                    workflow['similarity_score'] = similarity
                    results.append(workflow)
        else:
            # Fallback to database text search
            results = self.db.search_workflows(query=query, limit=limit)
        
        return results
    
    def get_recommendations(self, workflow_id: str, limit: int = 5) -> List[Dict]:
        """Get workflow recommendations based on similarity"""
        workflow = self.db.get_workflow(workflow_id)
        if not workflow:
            return []
        
        # Use title and description for similarity search
        query = f"{workflow.get('title', '')} {workflow.get('description', '')}"
        similar = self.search(query, use_semantic=True, limit=limit + 1)
        
        # Remove the original workflow from results
        recommendations = [w for w in similar if w['id'] != workflow_id][:limit]
        
        return recommendations

def main():
    """Test the database setup"""
    logger.info("Setting up AgentChef Workflow Knowledge Base")
    
    # Initialize knowledge base
    kb = WorkflowKnowledgeBase()
    
    # Add workflows from extracted data if available
    if Path("workflows_data.json").exists():
        count = kb.add_workflows_from_json("workflows_data.json")
        logger.info(f"Added {count} workflows to knowledge base")
        
        # Show stats
        stats = kb.db.get_stats()
        logger.info(f"Database stats: {stats}")
        
        # Test search
        results = kb.search("AI agent", limit=3)
        logger.info(f"Search results for 'AI agent': {len(results)} workflows found")
        for result in results:
            logger.info(f"  - {result['title']} (similarity: {result.get('similarity_score', 'N/A')})")
    
    else:
        logger.info("No workflow data found. Run the extractor first.")

if __name__ == "__main__":
    main()

