#!/usr/bin/env python3
"""
Test script for the enhanced AgentChef extractor with JSON downloading capabilities
"""

import os
import json
import time
from pathlib import Path
from agentchef_extractor import AgentChefExtractor
from database_setup import WorkflowKnowledgeBase

def test_basic_functionality():
    """Test basic extractor functionality"""
    print("🧪 Testing basic extractor functionality...")
    
    try:
        extractor = AgentChefExtractor(download_dir="test_downloads")
        print("✅ Extractor initialized successfully")
        
        # Test selenium setup
        extractor.setup_selenium(headless=True)
        print("✅ Selenium WebDriver setup successful")
        
        extractor.close_selenium()
        print("✅ Basic functionality test passed")
        return True
        
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        return False

def test_directory_structure():
    """Test that the directory structure is created correctly"""
    print("🧪 Testing directory structure creation...")
    
    try:
        test_dir = "test_structure"
        extractor = AgentChefExtractor(download_dir=test_dir)
        
        expected_dirs = [
            Path(test_dir),
            Path(test_dir) / "json_files",
            Path(test_dir) / "metadata", 
            Path(test_dir) / "logs"
        ]
        
        for dir_path in expected_dirs:
            if not dir_path.exists():
                print(f"❌ Directory not created: {dir_path}")
                return False
        
        print("✅ Directory structure test passed")
        return True
        
    except Exception as e:
        print(f"❌ Directory structure test failed: {e}")
        return False

def test_json_file_operations():
    """Test JSON file saving and loading"""
    print("🧪 Testing JSON file operations...")
    
    try:
        extractor = AgentChefExtractor(download_dir="test_json_ops")
        
        # Test data
        test_workflow_id = "test-workflow-123"
        test_json_data = {
            "id": test_workflow_id,
            "name": "Test Workflow",
            "nodes": [
                {"id": "node1", "type": "trigger"},
                {"id": "node2", "type": "action"}
            ],
            "edges": [
                {"from": "node1", "to": "node2"}
            ]
        }
        
        # Save JSON file
        extractor._save_json_file(test_workflow_id, test_json_data)
        
        # Check if file was created
        json_file_path = Path("test_json_ops") / "json_files" / f"{test_workflow_id}.json"
        if not json_file_path.exists():
            print(f"❌ JSON file not created: {json_file_path}")
            return False
        
        # Load and verify content
        with open(json_file_path, 'r', encoding='utf-8') as f:
            loaded_data = json.load(f)
        
        if loaded_data != test_json_data:
            print("❌ JSON file content doesn't match")
            return False
        
        print("✅ JSON file operations test passed")
        return True
        
    except Exception as e:
        print(f"❌ JSON file operations test failed: {e}")
        return False

def test_workflow_validation():
    """Test workflow JSON validation"""
    print("🧪 Testing workflow JSON validation...")
    
    try:
        extractor = AgentChefExtractor(download_dir="test_validation")
        
        # Valid workflow data
        valid_workflow = {
            "nodes": [{"id": "1", "type": "trigger"}],
            "edges": []
        }
        
        # Invalid data
        invalid_data = {
            "random": "data",
            "not": "workflow"
        }
        
        if not extractor._validate_workflow_json(valid_workflow):
            print("❌ Valid workflow not recognized")
            return False
        
        if extractor._validate_workflow_json(invalid_data):
            print("❌ Invalid data incorrectly validated")
            return False
        
        print("✅ Workflow validation test passed")
        return True
        
    except Exception as e:
        print(f"❌ Workflow validation test failed: {e}")
        return False

def test_download_stats():
    """Test download statistics tracking"""
    print("🧪 Testing download statistics...")
    
    try:
        extractor = AgentChefExtractor(download_dir="test_stats")
        
        # Initialize stats
        initial_stats = extractor.download_stats.copy()
        
        # Simulate some operations
        extractor.download_stats['total_processed'] = 10
        extractor.download_stats['json_downloaded'] = 7
        extractor.download_stats['metadata_only'] = 2
        extractor.download_stats['paid_skipped'] = 1
        extractor.download_stats['errors'] = 0
        
        # Save stats
        extractor._save_download_stats()
        
        # Check if stats file was created
        stats_files = list((Path("test_stats") / "logs").glob("extraction_stats_*.json"))
        if not stats_files:
            print("❌ Stats file not created")
            return False
        
        # Load and verify stats
        with open(stats_files[0], 'r') as f:
            saved_stats = json.load(f)
        
        if saved_stats['total_processed'] != 10:
            print("❌ Stats not saved correctly")
            return False
        
        print("✅ Download statistics test passed")
        return True
        
    except Exception as e:
        print(f"❌ Download statistics test failed: {e}")
        return False

def test_integration_with_database():
    """Test integration with the database system"""
    print("🧪 Testing database integration...")
    
    try:
        # Create test workflow data with JSON
        test_workflows = [
            {
                "id": "test-workflow-with-json",
                "title": "Test Workflow with JSON",
                "description": "A test workflow that includes JSON data",
                "category": "Testing",
                "author": "Test Author",
                "author_verified": True,
                "view_count": 100,
                "node_count": 5,
                "is_free": True,
                "created_date": "2024-01-01",
                "node_types": ["Trigger", "Action", "Condition"],
                "url": "https://test.com/workflow/test-workflow-with-json",
                "workflow_json": {
                    "nodes": [
                        {"id": "1", "type": "trigger", "name": "Start"},
                        {"id": "2", "type": "action", "name": "Process"}
                    ],
                    "edges": [{"from": "1", "to": "2"}]
                },
                "extracted_at": "2025-06-30T12:00:00Z"
            }
        ]
        
        # Save to JSON file
        test_file = "test_workflows_with_json.json"
        with open(test_file, 'w', encoding='utf-8') as f:
            json.dump(test_workflows, f, indent=2, ensure_ascii=False)
        
        # Test database integration
        kb = WorkflowKnowledgeBase(db_path="test_integration.db", vector_path="test_integration.pkl")
        count = kb.add_workflows_from_json(test_file)
        
        if count != 1:
            print(f"❌ Expected 1 workflow, got {count}")
            return False
        
        # Test retrieval
        workflow = kb.db.get_workflow("test-workflow-with-json")
        if not workflow:
            print("❌ Workflow not found in database")
            return False
        
        if not workflow.get('workflow_json'):
            print("❌ Workflow JSON not stored in database")
            return False
        
        # Clean up
        os.unlink(test_file)
        os.unlink("test_integration.db")
        os.unlink("test_integration.pkl")
        
        print("✅ Database integration test passed")
        return True
        
    except Exception as e:
        print(f"❌ Database integration test failed: {e}")
        return False

def cleanup_test_files():
    """Clean up test files and directories"""
    print("🧹 Cleaning up test files...")
    
    test_dirs = [
        "test_downloads",
        "test_structure", 
        "test_json_ops",
        "test_validation",
        "test_stats"
    ]
    
    for test_dir in test_dirs:
        try:
            import shutil
            if Path(test_dir).exists():
                shutil.rmtree(test_dir)
        except Exception as e:
            print(f"Warning: Could not remove {test_dir}: {e}")

def main():
    """Run all tests"""
    print("🚀 Starting Enhanced AgentChef Extractor Tests")
    print("=" * 60)
    
    tests = [
        test_basic_functionality,
        test_directory_structure,
        test_json_file_operations,
        test_workflow_validation,
        test_download_stats,
        test_integration_with_database
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            failed += 1
        print()
    
    print("=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📊 Success Rate: {(passed / (passed + failed)) * 100:.1f}%")
    
    if failed == 0:
        print("🎉 All tests passed! The enhanced extractor is ready to use.")
    else:
        print("⚠️ Some tests failed. Please check the implementation.")
    
    cleanup_test_files()

if __name__ == "__main__":
    main()
