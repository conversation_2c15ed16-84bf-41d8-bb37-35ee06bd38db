[{"id": "sample-workflow-1", "title": "AI Email Assistant Workflow", "description": "Automated email processing and response generation using AI. This workflow monitors incoming emails, categorizes them, and generates appropriate responses using natural language processing.", "category": "Email Automation", "author": "AgentChef Team", "author_verified": true, "view_count": 1250, "node_count": 8, "is_free": true, "created_date": "2024-01-15", "node_types": ["<PERSON><PERSON>", "AI Text Generator", "Conditional Logic", "<PERSON><PERSON>"], "url": "https://agentchef.vercel.app/workflow/sample-workflow-1", "extracted_at": "2025-06-30T11:00:00Z"}, {"id": "sample-workflow-2", "title": "E-commerce Order Processing Bot", "description": "Complete automation for e-commerce order processing including inventory checks, payment processing, and customer notifications. Integrates with popular e-commerce platforms.", "category": "E-commerce", "author": "Commerce Pro", "author_verified": false, "view_count": 890, "node_count": 12, "is_free": false, "created_date": "2024-02-20", "node_types": ["Webhook Trigger", "Database Query", "Payment Gateway", "Email Notification", "Inventory Manager"], "url": "https://agentchef.vercel.app/workflow/sample-workflow-2", "extracted_at": "2025-06-30T11:00:00Z"}, {"id": "sample-workflow-3", "title": "Social Media Content Scheduler", "description": "Automated social media posting across multiple platforms. Schedule posts, optimize timing, and track engagement metrics automatically.", "category": "Social Media", "author": "Social Guru", "author_verified": true, "view_count": 2100, "node_count": 6, "is_free": true, "created_date": "2024-03-10", "node_types": ["Schedule Trigger", "Content Generator", "Social Media API", "Analytics Tracker"], "url": "https://agentchef.vercel.app/workflow/sample-workflow-3", "extracted_at": "2025-06-30T11:00:00Z"}, {"id": "sample-workflow-4", "title": "Customer Support Chatbot", "description": "Intelligent customer support automation with natural language understanding, ticket routing, and escalation management. Reduces response time and improves customer satisfaction.", "category": "Customer Support", "author": "Support Master", "author_verified": true, "view_count": 1750, "node_count": 10, "is_free": false, "created_date": "2024-04-05", "node_types": ["<PERSON><PERSON>", "NLP Processor", "Knowledge Base", "Ticket System", "Escalation Logic"], "url": "https://agentchef.vercel.app/workflow/sample-workflow-4", "extracted_at": "2025-06-30T11:00:00Z"}, {"id": "sample-workflow-5", "title": "Data Pipeline Automation", "description": "Automated data extraction, transformation, and loading (ETL) pipeline for business intelligence. Processes data from multiple sources and generates reports.", "category": "Data Processing", "author": "Data Wizard", "author_verified": false, "view_count": 650, "node_count": 15, "is_free": true, "created_date": "2024-05-12", "node_types": ["Data Source", "Transform Engine", "Data Validator", "Database Loader", "Report Generator"], "url": "https://agentchef.vercel.app/workflow/sample-workflow-5", "extracted_at": "2025-06-30T11:00:00Z"}, {"id": "sample-workflow-6", "title": "Lead Generation and Qualification", "description": "Automated lead generation from multiple sources with intelligent qualification scoring. Integrates with CRM systems and marketing automation tools.", "category": "Marketing", "author": "Marketing Pro", "author_verified": true, "view_count": 1420, "node_count": 9, "is_free": false, "created_date": "2024-06-01", "node_types": ["Web Scraper", "Lead Scorer", "CRM Integration", "Email Automation", "Analytics"], "url": "https://agentchef.vercel.app/workflow/sample-workflow-6", "extracted_at": "2025-06-30T11:00:00Z"}]