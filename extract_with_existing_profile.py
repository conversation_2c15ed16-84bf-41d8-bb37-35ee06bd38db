#!/usr/bin/env python3
"""
AgentChef Extractor using existing Chrome profile for auto-authentication
"""

import time
import json
from pathlib import Path
from agentchef_extractor import AgentChefExtractor

def main():
    print("🔐 AgentChef Extractor with Existing Chrome Profile")
    print("=" * 60)
    print()
    print("This script will:")
    print("1. Use your existing Chrome profile (with saved logins)")
    print("2. Automatically access AgentChef with your saved credentials")
    print("3. Extract both metadata AND JSON workflow files")
    print("4. Save everything to organized folders")
    print()
    
    # Configuration
    max_workflows = input("How many workflows to extract? (default: 10): ").strip()
    if not max_workflows:
        max_workflows = 10
    else:
        try:
            max_workflows = int(max_workflows)
        except ValueError:
            max_workflows = 10
    
    download_dir = input("Download directory? (default: agentchef_profile_auth): ").strip()
    if not download_dir:
        download_dir = "agentchef_profile_auth"
    
    headless = input("Run in headless mode? (y/N): ").strip().lower() == 'y'
    
    print(f"\n📋 Configuration:")
    print(f"   Target workflows: {max_workflows}")
    print(f"   Download directory: {download_dir}")
    print(f"   Browser mode: {'Headless' if headless else 'Visible'}")
    print(f"   Authentication: Using existing Chrome profile")
    print(f"   JSON extraction: Enabled")
    print()
    
    response = input("Proceed with extraction? (y/N): ").strip().lower()
    if response != 'y':
        print("Extraction cancelled.")
        return
    
    print("\n🔄 Starting extraction with existing Chrome profile...")
    
    try:
        # Initialize extractor
        extractor = AgentChefExtractor(download_dir=download_dir)
        
        # Setup selenium with existing profile
        print("🌐 Setting up browser with your existing Chrome profile...")
        extractor.setup_selenium(headless=headless, use_existing_profile=True)
        
        # Test authentication by navigating to AgentChef
        print("🔐 Testing authentication with existing profile...")
        extractor.driver.get("https://agentchef.vercel.app")
        time.sleep(3)
        
        # Check if we need to log in
        current_url = extractor.driver.current_url
        page_source = extractor.driver.page_source.lower()
        
        if "login" in current_url.lower() or "sign in" in page_source:
            print("⚠️ Not automatically logged in - you may need to log in manually")
            if not headless:
                input("Please log in manually in the browser window, then press Enter...")
        else:
            print("✅ Already authenticated with existing profile!")
        
        extractor.is_authenticated = True
        
        # Navigate to browse page
        print("🔍 Loading browse page...")
        extractor.driver.get("https://agentchef.vercel.app/browse")
        time.sleep(5)
        
        # Extract workflow IDs
        workflow_ids = extractor.extract_workflow_ids_from_browse()
        
        if not workflow_ids:
            print("❌ No workflow IDs found on browse page")
            print("🔍 Debugging browse page...")
            
            page_title = extractor.driver.title
            print(f"   Page title: {page_title}")
            
            # Try scrolling to load more content
            print("   Trying to scroll and load more content...")
            for i in range(3):
                extractor.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(2)
            
            # Try again
            workflow_ids = extractor.extract_workflow_ids_from_browse()
            
            if not workflow_ids:
                print("   Still no workflows found - the page might not be loading correctly")
                return
        
        print(f"✅ Found {len(workflow_ids)} workflow IDs")
        
        # Limit to requested number
        workflow_ids = workflow_ids[:max_workflows]
        print(f"📊 Processing {len(workflow_ids)} workflows...")
        
        # Reset stats
        extractor.download_stats = {
            'total_processed': 0,
            'json_downloaded': 0,
            'metadata_only': 0,
            'paid_skipped': 0,
            'errors': 0
        }
        
        # Extract each workflow
        extracted_workflows = []
        
        for i, workflow_id in enumerate(workflow_ids):
            print(f"\n🔄 Processing workflow {i+1}/{len(workflow_ids)}: {workflow_id}")
            
            try:
                # Extract metadata and JSON
                metadata = extractor.extract_workflow_metadata(workflow_id)
                
                if metadata:
                    extracted_workflows.append(metadata)
                    extractor.download_stats['total_processed'] += 1
                    
                    # Save individual metadata
                    extractor._save_metadata_file(workflow_id, metadata)
                    
                    print(f"   ✅ Title: {metadata.title}")
                    print(f"   📄 Description: {metadata.description[:100]}...")
                    print(f"   💰 Free: {metadata.is_free}")
                    
                    if metadata.workflow_json:
                        print(f"   📄 JSON: Successfully extracted and saved!")
                    else:
                        print(f"   📄 JSON: Not extracted")
                
            except Exception as e:
                print(f"   ❌ Error: {e}")
                extractor.download_stats['errors'] += 1
            
            # Respectful delay
            time.sleep(2)
        
        # Save combined data
        extractor.save_workflow_data(extracted_workflows)
        extractor._save_download_stats()
        
        # Print final summary
        print("\n" + "="*60)
        print("📊 EXTRACTION SUMMARY")
        print("="*60)
        stats = extractor.download_stats
        print(f"Total workflows processed: {stats['total_processed']}")
        print(f"JSON files downloaded: {stats['json_downloaded']}")
        print(f"Metadata-only extractions: {stats['metadata_only']}")
        print(f"Paid workflows skipped: {stats['paid_skipped']}")
        print(f"Errors encountered: {stats['errors']}")
        
        if stats['total_processed'] > 0:
            json_rate = (stats['json_downloaded'] / stats['total_processed']) * 100
            print(f"JSON extraction success rate: {json_rate:.1f}%")
        
        print(f"\n📁 Files saved to: {download_dir}")
        print("="*60)
        
        # Show what files were created
        download_path = Path(download_dir)
        if download_path.exists():
            json_files = list((download_path / "json_files").glob("*.json"))
            metadata_files = list((download_path / "metadata").glob("*.json"))
            
            print(f"\n📄 Files created:")
            print(f"   JSON files: {len(json_files)}")
            print(f"   Metadata files: {len(metadata_files)}")
            print(f"   Combined data: workflows_data.json")
            
            if json_files:
                print(f"\n📄 JSON files downloaded:")
                for json_file in json_files:
                    workflow_id = json_file.stem
                    size = json_file.stat().st_size
                    print(f"   {workflow_id}.json ({size} bytes)")
                    
                    # Verify JSON content
                    try:
                        with open(json_file, 'r', encoding='utf-8') as f:
                            json_data = json.load(f)
                        
                        # Check if it's actual workflow JSON (not just metadata)
                        if isinstance(json_data, dict) and ('nodes' in json_data or 'connections' in json_data):
                            print(f"      ✅ Contains workflow data")
                        else:
                            print(f"      ⚠️ May be metadata only")
                    except:
                        print(f"      ❌ Invalid JSON")
        
        print("\n✅ Extraction completed!")
        
        if not headless:
            print("\n👀 Browser window will stay open for 30 seconds for inspection...")
            time.sleep(30)
        
    except KeyboardInterrupt:
        print("\n⚠️ Extraction interrupted by user")
    except Exception as e:
        print(f"\n❌ Extraction failed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if 'extractor' in locals() and extractor.driver:
            print("🔒 Closing browser...")
            extractor.close_selenium()

if __name__ == "__main__":
    main()
