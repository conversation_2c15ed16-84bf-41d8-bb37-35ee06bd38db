#!/usr/bin/env python3
"""
AgentChef Extractor with Interactive Authentication
This script runs with a visible browser for manual authentication and JSON extraction
"""

import time
from agentchef_extractor import AgentChefExtractor
from database_setup import WorkflowKnowledgeBase

def main():
    print("🚀 AgentChef Extractor with Interactive Authentication")
    print("=" * 60)
    print()
    print("This script will:")
    print("1. Open a visible browser window")
    print("2. Allow you to log in manually")
    print("3. Extract both metadata AND JSON files")
    print("4. Handle paid workflows appropriately")
    print()
    
    # Get user preferences
    max_workflows = input("How many workflows to extract? (default: 10): ").strip()
    if not max_workflows:
        max_workflows = 10
    else:
        try:
            max_workflows = int(max_workflows)
        except ValueError:
            max_workflows = 10
    
    download_dir = input("Download directory? (default: agentchef_authenticated): ").strip()
    if not download_dir:
        download_dir = "agentchef_authenticated"
    
    print(f"\n📋 Configuration:")
    print(f"   Target workflows: {max_workflows}")
    print(f"   Download directory: {download_dir}")
    print(f"   Browser mode: Visible (for authentication)")
    print(f"   JSON extraction: Enabled")
    print()
    
    response = input("Proceed with extraction? (y/N): ").strip().lower()
    if response != 'y':
        print("Extraction cancelled.")
        return
    
    print("\n🔄 Starting extraction with authentication...")
    
    try:
        # Initialize extractor
        extractor = AgentChefExtractor(download_dir=download_dir)
        
        # Run extraction with visible browser for authentication
        extractor.run_extraction(
            max_workflows=max_workflows,
            interactive_auth=True,  # Enable interactive authentication
            extract_json=True       # Enable JSON extraction
        )
        
        print("\n✅ Extraction completed!")
        
        # Show results
        print("\n📊 Results Summary:")
        stats = extractor.download_stats
        print(f"   Total processed: {stats['total_processed']}")
        print(f"   JSON downloaded: {stats['json_downloaded']}")
        print(f"   Metadata only: {stats['metadata_only']}")
        print(f"   Paid skipped: {stats['paid_skipped']}")
        print(f"   Errors: {stats['errors']}")
        
        if stats['total_processed'] > 0:
            json_rate = (stats['json_downloaded'] / stats['total_processed']) * 100
            print(f"   JSON success rate: {json_rate:.1f}%")
        
        print(f"\n📁 Files saved to: {download_dir}")
        
        # Ask about database import
        if stats['total_processed'] > 0:
            print("\n🗄️ Database Integration")
            response = input("Import extracted data into database? (y/N): ").strip().lower()
            if response == 'y':
                try:
                    kb = WorkflowKnowledgeBase()
                    count = kb.add_workflows_from_json("workflows_data.json")
                    print(f"✅ Imported {count} workflows into database")
                    
                    # Show database stats
                    db_stats = kb.db.get_stats()
                    print(f"📊 Database now contains {db_stats.get('total_workflows', 0)} total workflows")
                    
                except Exception as e:
                    print(f"❌ Database import failed: {e}")
        
    except KeyboardInterrupt:
        print("\n⚠️ Extraction interrupted by user")
    except Exception as e:
        print(f"\n❌ Extraction failed: {e}")
        print("Check the logs for more details")

if __name__ == "__main__":
    main()
