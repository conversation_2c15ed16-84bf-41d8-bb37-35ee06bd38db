[{"id": "ab2d1ec2-713b-4559-99d5-5aa8b4d4ef71", "title": "AI agent chat", "description": "This workflow employs OpenAI's language models and SerpAPI to create a responsive, intelligent conversational agent. It comes equipped with manual chat triggers and memory buffer capabilities to ensure seamless interactions.", "category": "", "author": "", "author_verified": false, "view_count": 0, "node_count": 0, "is_free": true, "created_date": null, "node_types": [], "url": "https://agentchef.vercel.app/workflow/ab2d1ec2-713b-4559-99d5-5aa8b4d4ef71", "extracted_at": "2025-06-30T13:24:54.272432"}, {"id": "c7b75383-dc78-4a85-a087-97a93c53068a", "title": "Creating an API endpoint", "description": "Task: Create a simple API endpoint using the Webhook and Respond to Webhook nodes", "category": "", "author": "", "author_verified": false, "view_count": 0, "node_count": 0, "is_free": true, "created_date": null, "node_types": [], "url": "https://agentchef.vercel.app/workflow/c7b75383-dc78-4a85-a087-97a93c53068a", "extracted_at": "2025-06-30T13:24:54.272432"}, {"id": "161b1a44-0292-44d8-ba33-2a76fbad1d12", "title": "Building Your First WhatsApp Chatbot", "description": "This n8n template builds a simple WhatsApp chabot acting as a Sales Agent. The Agent is backed by a product catalog vector store to better answer user's questions.", "category": "", "author": "a product catalog vector store to better answer user's questions.", "author_verified": false, "view_count": 0, "node_count": 0, "is_free": true, "created_date": null, "node_types": [], "url": "https://agentchef.vercel.app/workflow/161b1a44-0292-44d8-ba33-2a76fbad1d12", "extracted_at": "2025-06-30T13:24:54.272432"}, {"id": "3da8e99e-bf72-42f8-a4ca-eb9e9737baca", "title": "Scrape and summarize webpages with AI", "description": "This workflow integrates both web scraping and NLP functionalities. It uses HTML parsing to extract links, HTTP requests to fetch essay content, and AI-based summarization using GPT-4o. It's an excellent example of an end-to-end automated task that is not only efficient but also provides real value by summarizing valuable content.", "category": "", "author": "summarizing valuable content.", "author_verified": false, "view_count": 0, "node_count": 0, "is_free": true, "created_date": null, "node_types": [], "url": "https://agentchef.vercel.app/workflow/3da8e99e-bf72-42f8-a4ca-eb9e9737baca", "extracted_at": "2025-06-30T13:24:54.272432"}, {"id": "5f9e80ee-619c-4766-96ac-495f1f736361", "title": "Very quick quickstart", "description": "Want to learn the basics of n8n? Our comprehensive quick quickstart tutorial is here to guide you through the basics of n8n, step by step.", "category": "", "author": "step.", "author_verified": false, "view_count": 0, "node_count": 0, "is_free": true, "created_date": null, "node_types": [], "url": "https://agentchef.vercel.app/workflow/5f9e80ee-619c-4766-96ac-495f1f736361", "extracted_at": "2025-06-30T13:24:54.272432"}, {"id": "851902f2-92be-4115-b52a-3a97faa866bf", "title": "AI agent that can scrape webpages", "description": "⚙️🛠️🚀🤖🦾", "category": "", "author": "", "author_verified": false, "view_count": 0, "node_count": 0, "is_free": true, "created_date": null, "node_types": [], "url": "https://agentchef.vercel.app/workflow/851902f2-92be-4115-b52a-3a97faa866bf", "extracted_at": "2025-06-30T13:24:54.272432"}]