# Enhanced AgentChef Workflow Extractor - Usage Guide

## 🎯 Overview

The enhanced AgentChef extractor now provides comprehensive workflow data extraction including:

- ✅ **Metadata Extraction**: Title, description, author, view count, categories, etc.
- ✅ **JSON Workflow Files**: Actual workflow definitions with nodes, edges, and configurations
- ✅ **Paid Workflow Detection**: Automatically identifies and skips paid workflows during JSON extraction
- ✅ **Authentication Support**: <PERSON><PERSON> login for accessing protected content
- ✅ **Organized File Storage**: Structured directory layout for easy management
- ✅ **Comprehensive Logging**: Detailed extraction statistics and error reporting

## 📁 File Structure

When you run the extractor, it creates the following directory structure:

```
downloaded_workflows/
├── json_files/                 # Actual workflow JSON files
│   ├── workflow-id-1.json
│   ├── workflow-id-2.json
│   └── ...
├── metadata/                   # Individual metadata files
│   ├── workflow-id-1_metadata.json
│   ├── workflow-id-2_metadata.json
│   └── ...
├── logs/                       # Extraction statistics and logs
│   ├── extraction_stats_20250630_120000.json
│   └── ...
└── workflows_data.json         # Combined metadata file
```

## 🚀 Basic Usage

### 1. Simple Extraction (Metadata + JSON)

```bash
# Extract 10 workflows with both metadata and JSON files
python agentchef_extractor.py --max-workflows 10
```

### 2. Metadata Only (Skip JSON)

```bash
# Extract only metadata, skip JSON files
python agentchef_extractor.py --max-workflows 20 --no-json
```

### 3. Custom Download Directory

```bash
# Use custom directory for downloads
python agentchef_extractor.py --max-workflows 15 --download-dir "my_workflows"
```

### 4. Non-Interactive Mode

```bash
# Run without interactive authentication prompts
python agentchef_extractor.py --max-workflows 10 --no-interactive
```

## 🔐 Authentication Options

### Interactive Authentication (Recommended)

```bash
# The extractor will prompt you to log in manually
python agentchef_extractor.py --max-workflows 10
# Follow the prompts to log in through the browser
```

### Automatic Authentication

```bash
# Provide credentials directly (use with caution)
python agentchef_extractor.py --max-workflows 10 --email "<EMAIL>" --password "yourpassword"
```

### No Authentication

```bash
# Skip authentication entirely (may limit access to some workflows)
python agentchef_extractor.py --max-workflows 10 --no-interactive
```

## 📊 Understanding the Output

### Extraction Statistics

After each run, you'll see a summary like this:

```
============================================================
EXTRACTION SUMMARY
============================================================
Total workflows processed: 10
JSON files downloaded: 7
Metadata-only extractions: 2
Paid workflows skipped: 1
Errors encountered: 0
JSON extraction success rate: 70.0%
Files saved to: downloaded_workflows
============================================================
```

### File Types Generated

1. **JSON Files** (`json_files/workflow-id.json`):
   - Complete workflow definitions
   - Node configurations
   - Edge connections
   - Workflow settings

2. **Metadata Files** (`metadata/workflow-id_metadata.json`):
   - Title, description, author
   - View count, node count
   - Category, pricing info
   - Extraction timestamp

3. **Combined Data** (`workflows_data.json`):
   - All metadata in one file
   - Compatible with database import
   - Includes JSON availability flags

4. **Statistics** (`logs/extraction_stats_*.json`):
   - Detailed extraction metrics
   - Success/failure rates
   - Timestamp information

## 🎛️ Advanced Configuration

### Command Line Options

```bash
python agentchef_extractor.py [OPTIONS]

Options:
  --max-workflows INT     Maximum number of workflows to extract (default: 10)
  --email TEXT           Email for authentication
  --password TEXT        Password for authentication  
  --no-interactive       Disable interactive authentication
  --no-json             Skip JSON extraction, metadata only
  --download-dir TEXT    Directory for downloaded files (default: downloaded_workflows)
  --help                Show help message
```

### Programmatic Usage

```python
from agentchef_extractor import AgentChefExtractor

# Initialize extractor
extractor = AgentChefExtractor(download_dir="my_custom_dir")

# Run extraction with custom parameters
extractor.run_extraction(
    max_workflows=25,
    email="<EMAIL>",
    password="yourpassword", 
    interactive_auth=False,
    extract_json=True
)
```

## 💰 Paid Workflow Handling

The extractor automatically detects paid workflows and handles them intelligently:

- ✅ **Metadata**: Always extracted for both free and paid workflows
- ✅ **JSON Files**: Only downloaded for FREE workflows
- ⚠️ **Paid Workflows**: Metadata extracted, JSON download skipped
- 📊 **Reporting**: Paid workflows are counted and reported in statistics

### Example Output for Paid Workflows

```
INFO - Skipping paid workflow: abc123 - Premium Email Automation
INFO - Metadata extracted for paid workflow: abc123
```

## 🔍 JSON Extraction Strategies

The extractor uses multiple strategies to obtain JSON files:

1. **Download Buttons**: Looks for and clicks download/export buttons
2. **Copy Functionality**: Uses copy-to-clipboard features
3. **API Access**: Attempts direct API endpoint access
4. **Page Source**: Extracts embedded JSON from page source

## 📈 Integration with Database

### Import Extracted Data

```python
from database_setup import WorkflowKnowledgeBase

# Initialize knowledge base
kb = WorkflowKnowledgeBase()

# Import extracted workflows
count = kb.add_workflows_from_json("downloaded_workflows/workflows_data.json")
print(f"Imported {count} workflows")
```

### Search with JSON Data

```python
# Search workflows
results = kb.search("email automation", use_semantic=True, limit=10)

# Access JSON data for workflows that have it
for workflow in results:
    if workflow.get('workflow_json'):
        print(f"Workflow {workflow['title']} has JSON data available")
```

## 🛠️ Troubleshooting

### Common Issues

1. **No JSON Files Downloaded**:
   - Check if workflows are paid (they'll be skipped)
   - Verify authentication is working
   - Try interactive mode for manual login

2. **Authentication Failures**:
   - Use interactive mode: remove `--no-interactive`
   - Check credentials if using automatic auth
   - Some sites may require 2FA

3. **Download Errors**:
   - Check internet connection
   - Verify Chrome/ChromeDriver installation
   - Try reducing `--max-workflows` number

4. **Permission Errors**:
   - Ensure write permissions to download directory
   - Try running as administrator if needed

### Debug Mode

For detailed debugging, check the log files:

```bash
# View extraction logs
cat agentchef_extraction.log

# View download statistics
cat downloaded_workflows/logs/extraction_stats_*.json
```

## 📋 Best Practices

### 1. Start Small
```bash
# Test with a small number first
python agentchef_extractor.py --max-workflows 5
```

### 2. Use Interactive Auth
```bash
# Interactive auth is more reliable
python agentchef_extractor.py --max-workflows 10
# (don't use --no-interactive)
```

### 3. Monitor Progress
```bash
# Watch the logs in real-time
tail -f agentchef_extraction.log
```

### 4. Backup Important Data
```bash
# Copy your downloads before re-running
cp -r downloaded_workflows downloaded_workflows_backup
```

### 5. Respect Rate Limits
- The extractor includes built-in delays
- Don't run multiple instances simultaneously
- Be respectful to the AgentChef servers

## 🎉 Success Indicators

You'll know the extraction is working well when you see:

- ✅ High JSON extraction success rate (>60%)
- ✅ Low error count
- ✅ Proper file organization in download directory
- ✅ Both metadata and JSON files being created
- ✅ Paid workflows being properly identified and skipped

## 📞 Support

If you encounter issues:

1. Check the troubleshooting section above
2. Review the log files for detailed error messages
3. Try running the test script: `python test_enhanced_extractor.py`
4. Start with a smaller number of workflows to isolate issues

---

**Happy extracting! 🚀**
