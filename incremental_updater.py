#!/usr/bin/env python3
"""
Incremental Updater for AgentChef Workflow Knowledge Base

This script handles incremental updates to detect and sync only changed workflows,
maintaining the knowledge base with minimal overhead.
"""

import json
import hashlib
import time
import schedule
from datetime import datetime, timedelta
from typing import Dict, List, Set, Optional
from pathlib import Path
import logging

from agentchef_extractor import AgentChefExtractor
from database_setup import WorkflowKnowledgeBase

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('incremental_update.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class IncrementalUpdater:
    """Handles incremental updates for the workflow knowledge base"""
    
    def __init__(self, kb_path: str = "workflows.db", sync_file: str = "last_sync.json"):
        self.kb = WorkflowKnowledgeBase(kb_path)
        self.extractor = AgentChefExtractor()
        self.sync_file = sync_file
        self.stats_file = "update_stats.json"
        self.last_sync_data = self.load_sync_data()
        
    def load_sync_data(self) -> Dict:
        """Load last sync data"""
        if Path(self.sync_file).exists():
            try:
                with open(self.sync_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Error loading sync data: {e}")
        
        return {
            'last_sync_time': None,
            'last_full_sync': None,
            'workflow_hashes': {},
            'total_workflows': 0
        }
    
    def save_sync_data(self):
        """Save sync data"""
        try:
            with open(self.sync_file, 'w') as f:
                json.dump(self.last_sync_data, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving sync data: {e}")
    
    def calculate_workflow_hash(self, workflow_data: Dict) -> str:
        """Calculate hash for workflow data to detect changes"""
        # Create a stable hash based on key fields
        hash_data = {
            'title': workflow_data.get('title', ''),
            'description': workflow_data.get('description', ''),
            'view_count': workflow_data.get('view_count', 0),
            'node_count': workflow_data.get('node_count', 0),
            'category': workflow_data.get('category', ''),
            'author': workflow_data.get('author', ''),
            'is_free': workflow_data.get('is_free', True)
        }
        
        hash_str = json.dumps(hash_data, sort_keys=True)
        return hashlib.md5(hash_str.encode()).hexdigest()
    
    def detect_changes(self, current_workflows: List[Dict]) -> Dict[str, List[str]]:
        """Detect new, updated, and deleted workflows"""
        current_ids = set()
        current_hashes = {}
        
        # Process current workflows
        for workflow in current_workflows:
            workflow_id = workflow['id']
            current_ids.add(workflow_id)
            current_hashes[workflow_id] = self.calculate_workflow_hash(workflow)
        
        # Get previous state
        previous_ids = set(self.last_sync_data['workflow_hashes'].keys())
        previous_hashes = self.last_sync_data['workflow_hashes']
        
        # Detect changes
        new_workflows = current_ids - previous_ids
        deleted_workflows = previous_ids - current_ids
        updated_workflows = []
        
        for workflow_id in current_ids & previous_ids:
            if current_hashes[workflow_id] != previous_hashes.get(workflow_id):
                updated_workflows.append(workflow_id)
        
        return {
            'new': list(new_workflows),
            'updated': updated_workflows,
            'deleted': list(deleted_workflows),
            'current_hashes': current_hashes
        }
    
    def run_incremental_update(self, max_workflows: int = 100) -> Dict:
        """Run incremental update process"""
        logger.info("Starting incremental update")
        start_time = datetime.now()
        
        try:
            # Extract current workflow data
            logger.info("Extracting current workflow data...")
            self.extractor.setup_selenium()
            
            # Get workflow IDs
            workflow_ids = self.extractor.extract_workflow_ids_from_browse()
            if not workflow_ids:
                logger.error("No workflow IDs found")
                return {'status': 'error', 'message': 'No workflows found'}
            
            # Limit for incremental update
            workflow_ids = workflow_ids[:max_workflows]
            logger.info(f"Processing {len(workflow_ids)} workflows for change detection")
            
            # Extract metadata for all workflows
            current_workflows = []
            for i, workflow_id in enumerate(workflow_ids):
                if i % 10 == 0:
                    logger.info(f"Processing workflow {i+1}/{len(workflow_ids)}")
                
                metadata = self.extractor.extract_workflow_metadata(workflow_id)
                if metadata:
                    workflow_data = {
                        'id': metadata.id,
                        'title': metadata.title,
                        'description': metadata.description,
                        'category': metadata.category,
                        'author': metadata.author,
                        'author_verified': metadata.author_verified,
                        'view_count': metadata.view_count,
                        'node_count': metadata.node_count,
                        'is_free': metadata.is_free,
                        'created_date': metadata.created_date,
                        'node_types': metadata.node_types,
                        'url': metadata.url,
                        'extracted_at': datetime.now().isoformat()
                    }
                    current_workflows.append(workflow_data)
                
                # Rate limiting
                time.sleep(0.5)
            
            # Detect changes
            changes = self.detect_changes(current_workflows)
            
            # Apply updates
            update_stats = {
                'new_count': len(changes['new']),
                'updated_count': len(changes['updated']),
                'deleted_count': len(changes['deleted']),
                'processed_workflows': [],
                'errors': []
            }
            
            # Process new and updated workflows
            workflows_to_update = []
            for workflow in current_workflows:
                if workflow['id'] in changes['new'] or workflow['id'] in changes['updated']:
                    workflows_to_update.append(workflow)
            
            if workflows_to_update:
                # Save to temporary file
                temp_file = "temp_workflows_update.json"
                with open(temp_file, 'w', encoding='utf-8') as f:
                    json.dump(workflows_to_update, f, indent=2, ensure_ascii=False)
                
                # Add to knowledge base
                added_count = self.kb.add_workflows_from_json(temp_file)
                update_stats['processed_workflows'] = [w['id'] for w in workflows_to_update]
                
                # Clean up temp file
                Path(temp_file).unlink(missing_ok=True)
            
            # Update sync data
            self.last_sync_data.update({
                'last_sync_time': datetime.now().isoformat(),
                'workflow_hashes': changes['current_hashes'],
                'total_workflows': len(current_workflows)
            })
            self.save_sync_data()
            
            # Save update statistics
            end_time = datetime.now()
            update_stats.update({
                'status': 'success',
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'duration_seconds': (end_time - start_time).total_seconds(),
                'total_workflows_checked': len(current_workflows)
            })
            
            self.save_update_stats(update_stats)
            
            logger.info(f"Incremental update completed: {update_stats['new_count']} new, "
                       f"{update_stats['updated_count']} updated, {update_stats['deleted_count']} deleted")
            
            return update_stats
            
        except Exception as e:
            logger.error(f"Error during incremental update: {e}")
            return {'status': 'error', 'message': str(e)}
        
        finally:
            self.extractor.close_selenium()
    
    def save_update_stats(self, stats: Dict):
        """Save update statistics"""
        try:
            # Load existing stats
            all_stats = []
            if Path(self.stats_file).exists():
                with open(self.stats_file, 'r') as f:
                    all_stats = json.load(f)
            
            # Add new stats
            all_stats.append(stats)
            
            # Keep only last 100 updates
            all_stats = all_stats[-100:]
            
            # Save
            with open(self.stats_file, 'w') as f:
                json.dump(all_stats, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving update stats: {e}")

def main():
    """Run incremental update"""
    updater = IncrementalUpdater()
    result = updater.run_incremental_update(max_workflows=50)
    
    if result['status'] == 'success':
        logger.info("Incremental update completed successfully")
    else:
        logger.error(f"Incremental update failed: {result.get('message', 'Unknown error')}")

if __name__ == "__main__":
    main()
