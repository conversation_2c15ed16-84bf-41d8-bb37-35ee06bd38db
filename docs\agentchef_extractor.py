#!/usr/bin/env python3
"""
AgentChef Workflow Extractor

This script extracts workflow data from the AgentChef webapp at agentchef.vercel.app
and stores it in a local database for building a knowledge base.
"""

import requests
import json
import time
import re
import hashlib
from datetime import datetime
from typing import List, Dict, Optional
from urllib.parse import urljoin, urlparse
from dataclasses import dataclass
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('agentchef_extraction.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class WorkflowMetadata:
    """Data class for workflow metadata"""
    id: str
    title: str
    description: str
    category: str
    author: str
    author_verified: bool
    view_count: int
    node_count: int
    is_free: bool
    created_date: Optional[str]
    node_types: List[str]
    url: str
    workflow_json: Optional[Dict] = None

class AgentChefExtractor:
    """Main extractor class for AgentChef workflows"""
    
    def __init__(self, base_url: str = "https://agentchef.vercel.app"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.driver = None
        self.workflows = []
        
    def setup_selenium(self):
        """Setup Selenium WebDriver"""
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            logger.info("Selenium WebDriver initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Selenium WebDriver: {e}")
            raise
    
    def close_selenium(self):
        """Close Selenium WebDriver"""
        if self.driver:
            self.driver.quit()
            logger.info("Selenium WebDriver closed")
    
    def extract_workflow_ids_from_browse(self, page_url: str = None) -> List[str]:
        """Extract workflow IDs from the browse page"""
        if not page_url:
            page_url = f"{self.base_url}/browse"
        
        if not self.driver:
            self.setup_selenium()
        
        workflow_ids = []
        
        try:
            logger.info(f"Loading browse page: {page_url}")
            self.driver.get(page_url)
            
            # Wait for page to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Scroll to load more content
            last_height = self.driver.execute_script("return document.body.scrollHeight")
            scroll_attempts = 0
            max_scrolls = 10  # Limit scrolling to avoid infinite loops
            
            while scroll_attempts < max_scrolls:
                # Scroll down
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(2)  # Wait for content to load
                
                # Check if new content loaded
                new_height = self.driver.execute_script("return document.body.scrollHeight")
                if new_height == last_height:
                    break
                    
                last_height = new_height
                scroll_attempts += 1
                logger.info(f"Scrolled {scroll_attempts} times, page height: {new_height}")
            
            # Extract workflow links
            workflow_links = self.driver.find_elements(By.CSS_SELECTOR, 'a[href*="/workflow/"]')
            
            for link in workflow_links:
                href = link.get_attribute('href')
                if href and '/workflow/' in href:
                    workflow_id = href.split('/workflow/')[-1]
                    if workflow_id and workflow_id not in workflow_ids:
                        workflow_ids.append(workflow_id)
            
            logger.info(f"Found {len(workflow_ids)} unique workflow IDs")
            
        except Exception as e:
            logger.error(f"Error extracting workflow IDs: {e}")
        
        return workflow_ids
    
    def extract_workflow_metadata(self, workflow_id: str) -> Optional[WorkflowMetadata]:
        """Extract metadata for a specific workflow"""
        workflow_url = f"{self.base_url}/workflow/{workflow_id}"
        
        if not self.driver:
            self.setup_selenium()
        
        try:
            logger.info(f"Extracting metadata for workflow: {workflow_id}")
            self.driver.get(workflow_url)
            
            # Wait for page to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "h1"))
            )
            
            # Extract title
            title_element = self.driver.find_element(By.TAG_NAME, "h1")
            title = title_element.text.strip()
            
            # Extract description
            description = ""
            try:
                # Look for description in various possible locations
                desc_selectors = [
                    'p:first-of-type',
                    '[class*="description"]',
                    '[class*="summary"]'
                ]
                for selector in desc_selectors:
                    try:
                        desc_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                        description = desc_element.text.strip()
                        if description:
                            break
                    except NoSuchElementException:
                        continue
            except Exception as e:
                logger.warning(f"Could not extract description for {workflow_id}: {e}")
            
            # Extract other metadata
            metadata = WorkflowMetadata(
                id=workflow_id,
                title=title,
                description=description,
                category="",  # Will be extracted from page
                author="",    # Will be extracted from page
                author_verified=False,
                view_count=0,
                node_count=0,
                is_free=True,
                created_date=None,
                node_types=[],
                url=workflow_url
            )
            
            # Try to extract additional metadata from the page
            self._extract_additional_metadata(metadata)
            
            return metadata
            
        except Exception as e:
            logger.error(f"Error extracting metadata for workflow {workflow_id}: {e}")
            return None
    
    def _extract_additional_metadata(self, metadata: WorkflowMetadata):
        """Extract additional metadata from the current page"""
        try:
            # Extract view count
            view_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'views')]")
            for element in view_elements:
                text = element.text
                view_match = re.search(r'([\d.]+[kK]?)\s*views', text)
                if view_match:
                    view_str = view_match.group(1)
                    if 'k' in view_str.lower():
                        metadata.view_count = int(float(view_str.lower().replace('k', '')) * 1000)
                    else:
                        metadata.view_count = int(view_str)
                    break
            
            # Extract node count
            node_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'nodes')]")
            for element in node_elements:
                text = element.text
                node_match = re.search(r'(\d+)\s*nodes', text)
                if node_match:
                    metadata.node_count = int(node_match.group(1))
                    break
            
            # Extract category
            category_elements = self.driver.find_elements(By.CSS_SELECTOR, '[class*="category"], [class*="tag"]')
            for element in category_elements:
                text = element.text.strip()
                if text and len(text) < 50:  # Reasonable category length
                    metadata.category = text
                    break
            
            # Extract author
            author_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'by ')]")
            for element in author_elements:
                text = element.text
                author_match = re.search(r'by\s+(.+)', text)
                if author_match:
                    metadata.author = author_match.group(1).strip()
                    break
            
            # Check if free
            free_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'Free')]")
            metadata.is_free = len(free_elements) > 0
            
        except Exception as e:
            logger.warning(f"Error extracting additional metadata: {e}")
    
    def attempt_json_extraction(self, workflow_id: str) -> Optional[Dict]:
        """Attempt to extract workflow JSON"""
        if not self.driver:
            return None
        
        try:
            # Look for JSON download/copy buttons
            json_buttons = self.driver.find_elements(By.XPATH, 
                "//*[contains(text(), 'JSON') or contains(text(), 'Download') or contains(text(), 'Copy')]")
            
            for button in json_buttons:
                try:
                    # Try clicking the button
                    self.driver.execute_script("arguments[0].click();", button)
                    time.sleep(1)
                    
                    # Check if JSON data appeared in clipboard or download
                    # This is a simplified approach - in practice, you'd need to handle
                    # clipboard access or file downloads
                    
                except Exception as e:
                    logger.warning(f"Failed to click JSON button: {e}")
                    continue
            
        except Exception as e:
            logger.warning(f"Error attempting JSON extraction for {workflow_id}: {e}")
        
        return None
    
    def save_workflow_data(self, workflows: List[WorkflowMetadata], filename: str = "workflows_data.json"):
        """Save extracted workflow data to JSON file"""
        data = []
        for workflow in workflows:
            data.append({
                'id': workflow.id,
                'title': workflow.title,
                'description': workflow.description,
                'category': workflow.category,
                'author': workflow.author,
                'author_verified': workflow.author_verified,
                'view_count': workflow.view_count,
                'node_count': workflow.node_count,
                'is_free': workflow.is_free,
                'created_date': workflow.created_date,
                'node_types': workflow.node_types,
                'url': workflow.url,
                'extracted_at': datetime.now().isoformat()
            })
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Saved {len(data)} workflows to {filename}")
    
    def run_extraction(self, max_workflows: int = 50):
        """Run the complete extraction process"""
        try:
            logger.info("Starting AgentChef workflow extraction")
            
            # Extract workflow IDs
            workflow_ids = self.extract_workflow_ids_from_browse()
            
            if not workflow_ids:
                logger.error("No workflow IDs found")
                return
            
            # Limit the number of workflows for initial testing
            workflow_ids = workflow_ids[:max_workflows]
            logger.info(f"Processing {len(workflow_ids)} workflows")
            
            # Extract metadata for each workflow
            extracted_workflows = []
            for i, workflow_id in enumerate(workflow_ids):
                logger.info(f"Processing workflow {i+1}/{len(workflow_ids)}: {workflow_id}")
                
                metadata = self.extract_workflow_metadata(workflow_id)
                if metadata:
                    extracted_workflows.append(metadata)
                
                # Add delay to be respectful to the server
                time.sleep(1)
            
            # Save the extracted data
            self.save_workflow_data(extracted_workflows)
            
            logger.info(f"Extraction completed. Processed {len(extracted_workflows)} workflows")
            
        except Exception as e:
            logger.error(f"Error during extraction: {e}")
        finally:
            self.close_selenium()

if __name__ == "__main__":
    extractor = AgentChefExtractor()
    extractor.run_extraction(max_workflows=10)  # Start with 10 workflows for testing

