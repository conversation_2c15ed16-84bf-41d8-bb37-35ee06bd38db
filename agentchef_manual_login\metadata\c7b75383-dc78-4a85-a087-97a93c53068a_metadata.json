{"id": "c7b75383-dc78-4a85-a087-97a93c53068a", "title": "Creating an API endpoint", "description": "Task: Create a simple API endpoint using the Webhook and Respond to Webhook nodes", "category": "", "author": "", "author_verified": false, "view_count": 0, "node_count": 0, "is_free": true, "created_date": null, "node_types": [], "url": "https://agentchef.vercel.app/workflow/c7b75383-dc78-4a85-a087-97a93c53068a", "has_json": true, "extracted_at": "2025-06-30T13:23:27.436039"}