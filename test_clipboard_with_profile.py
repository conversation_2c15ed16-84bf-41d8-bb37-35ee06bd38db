#!/usr/bin/env python3
"""
Test clipboard extraction with existing Chrome profile
"""

import time
import json
import os
from pathlib import Path
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

def setup_browser_with_profile():
    """Setup browser with existing Chrome profile"""
    chrome_options = Options()
    
    # Use existing Chrome profile
    profile_paths = [
        os.path.expanduser("~\\AppData\\Local\\Google\\Chrome\\User Data"),  # Windows
        os.path.expanduser("~/Library/Application Support/Google/Chrome"),    # macOS
        os.path.expanduser("~/.config/google-chrome")                        # Linux
    ]
    
    for profile_path in profile_paths:
        if os.path.exists(profile_path):
            chrome_options.add_argument(f"--user-data-dir={profile_path}")
            chrome_options.add_argument("--profile-directory=Default")
            print(f"✅ Using existing Chrome profile: {profile_path}")
            break
    else:
        print("⚠️ Could not find existing Chrome profile")
    
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-web-security')
    chrome_options.add_argument('--allow-running-insecure-content')
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    return driver

def test_single_workflow_clipboard(driver, workflow_id):
    """Test clipboard extraction on a single workflow"""
    workflow_url = f"https://agentchef.vercel.app/workflow/{workflow_id}"
    print(f"🌐 Navigating to: {workflow_url}")
    driver.get(workflow_url)
    time.sleep(5)
    
    print("🔍 Looking for 'Copy Workflow JSON' button...")
    
    # Find all buttons and look for the copy button
    buttons = driver.find_elements(By.TAG_NAME, "button")
    copy_button = None
    
    for button in buttons:
        try:
            button_text = button.text.strip()
            if "Copy Workflow JSON" in button_text or "Copy JSON" in button_text:
                copy_button = button
                print(f"✅ Found copy button: '{button_text}'")
                break
        except:
            continue
    
    if not copy_button:
        print("❌ No 'Copy Workflow JSON' button found")
        print("🔍 Available buttons:")
        for i, button in enumerate(buttons[:10]):
            try:
                text = button.text.strip()
                if text:
                    print(f"   {i+1}. '{text}'")
            except:
                pass
        return None
    
    print("🖱️ Clicking copy button...")
    try:
        # Try multiple click methods
        try:
            copy_button.click()
        except:
            driver.execute_script("arguments[0].click();", copy_button)
        
        time.sleep(2)  # Wait for copy to complete
        
        print("📋 Attempting to extract clipboard content...")
        
        # Method 1: Create textarea and paste
        driver.execute_script("""
            var textarea = document.createElement('textarea');
            textarea.id = 'clipboard-extractor';
            textarea.style.position = 'fixed';
            textarea.style.top = '0';
            textarea.style.left = '0';
            textarea.style.width = '100px';
            textarea.style.height = '100px';
            textarea.style.opacity = '1';
            textarea.style.zIndex = '9999';
            document.body.appendChild(textarea);
            textarea.focus();
            textarea.select();
        """)
        
        time.sleep(1)
        
        # Find textarea and paste
        textarea = driver.find_element(By.ID, "clipboard-extractor")
        textarea.send_keys(Keys.CONTROL + "v")
        time.sleep(2)
        
        content = textarea.get_attribute("value")
        
        # Clean up
        driver.execute_script("document.getElementById('clipboard-extractor').remove();")
        
        if content and content.strip():
            print(f"✅ Clipboard content extracted! Length: {len(content)} characters")
            print(f"📄 First 200 characters: {content[:200]}...")
            
            # Try to parse as JSON
            try:
                json_data = json.loads(content)
                print("✅ Content is valid JSON!")
                print(f"📊 JSON structure: {list(json_data.keys()) if isinstance(json_data, dict) else type(json_data)}")
                return json_data
            except json.JSONDecodeError:
                print("❌ Content is not valid JSON")
                print(f"📄 Raw content: {content}")
                return content
        else:
            print("❌ No content extracted from clipboard")
            return None
            
    except Exception as e:
        print(f"❌ Error during clipboard extraction: {e}")
        return None

def save_extracted_content(workflow_id, content, output_dir="test_clipboard_output"):
    """Save extracted content"""
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    if isinstance(content, dict):
        # Save as JSON
        json_file = output_path / f"{workflow_id}_workflow.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(content, f, indent=2, ensure_ascii=False)
        print(f"💾 Saved workflow JSON to: {json_file}")
        return json_file
    else:
        # Save as text
        text_file = output_path / f"{workflow_id}_raw.txt"
        with open(text_file, 'w', encoding='utf-8') as f:
            f.write(str(content))
        print(f"💾 Saved raw content to: {text_file}")
        return text_file

def main():
    print("🧪 Clipboard Extraction Test with Existing Chrome Profile")
    print("=" * 60)
    
    # Test workflow ID
    test_workflow_id = "3da8e99e-bf72-42f8-a4ca-eb9e9737baca"
    
    print(f"🎯 Testing with workflow: {test_workflow_id}")
    print("🔐 Using your existing Chrome profile for authentication")
    print()
    
    input("Press Enter to start the test...")
    
    driver = None
    try:
        print("🚀 Starting browser with existing profile...")
        driver = setup_browser_with_profile()
        
        print("🌐 Navigating to AgentChef...")
        driver.get("https://agentchef.vercel.app")
        time.sleep(3)
        
        # Check if logged in
        current_url = driver.current_url
        if "login" in current_url.lower():
            print("⚠️ Not automatically logged in")
            input("Please log in manually, then press Enter...")
        else:
            print("✅ Already logged in with existing profile!")
        
        # Test clipboard extraction
        content = test_single_workflow_clipboard(driver, test_workflow_id)
        
        if content:
            saved_file = save_extracted_content(test_workflow_id, content)
            print(f"🎉 Test successful! Content saved to: {saved_file}")
        else:
            print("❌ Test failed - no content extracted")
        
        print("\n🔍 Browser will stay open for manual inspection...")
        input("Press Enter to close browser...")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if driver:
            driver.quit()

if __name__ == "__main__":
    main()
