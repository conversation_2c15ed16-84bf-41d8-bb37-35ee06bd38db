<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AgentChef Workflow Knowledge Base</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .search-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .search-form {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .search-input {
            flex: 1;
            min-width: 300px;
            padding: 12px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }
        
        .search-input:focus {
            border-color: #667eea;
        }
        
        .search-btn {
            padding: 12px 30px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .search-btn:hover {
            transform: translateY(-2px);
        }
        
        .filters {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
        }
        
        .filter-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .filter-select {
            padding: 8px 15px;
            border: 1px solid #ddd;
            border-radius: 20px;
            background: white;
            outline: none;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .stats-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .stat-label {
            font-size: 1.1rem;
            color: #666;
        }
        
        .results-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .workflow-card {
            border: 1px solid #e1e5e9;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            transition: all 0.3s;
            cursor: pointer;
        }
        
        .workflow-card:hover {
            border-color: #667eea;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
        }
        
        .workflow-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .workflow-description {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        
        .workflow-meta {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            font-size: 0.9rem;
            color: #888;
        }
        
        .meta-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .badge {
            background: #667eea;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
        }
        
        .badge.free {
            background: #28a745;
        }
        
        .badge.paid {
            background: #ffc107;
            color: #333;
        }
        
        .similarity-score {
            background: #17a2b8;
            color: white;
            padding: 4px 8px;
            border-radius: 10px;
            font-size: 0.8rem;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .update-section {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .update-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }
        
        .update-btn:hover {
            background: #218838;
        }
        
        .update-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        @media (max-width: 768px) {
            .search-form {
                flex-direction: column;
            }
            
            .search-input {
                min-width: auto;
            }
            
            .filters {
                flex-direction: column;
                align-items: stretch;
            }
            
            .workflow-meta {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AgentChef Workflow Knowledge Base</h1>
            <p>Search and discover AI automation workflows</p>
        </div>
        
        <div class="update-section">
            <h3>System Management</h3>
            <button id="updateBtn" class="update-btn" onclick="triggerUpdate()">
                🔄 Update Workflows
            </button>
            <span id="updateStatus"></span>
        </div>
        
        <div class="stats-section" id="statsSection">
            <!-- Stats will be loaded here -->
        </div>
        
        <div class="search-section">
            <div class="search-form">
                <input type="text" id="searchInput" class="search-input" 
                       placeholder="Search workflows (e.g., 'AI agent', 'automation', 'email')">
                <button class="search-btn" onclick="searchWorkflows()">🔍 Search</button>
            </div>
            
            <div class="filters">
                <div class="filter-group">
                    <label>Category:</label>
                    <select id="categoryFilter" class="filter-select">
                        <option value="">All Categories</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label>Type:</label>
                    <select id="typeFilter" class="filter-select">
                        <option value="">All Types</option>
                        <option value="true">Free Only</option>
                        <option value="false">Paid Only</option>
                    </select>
                </div>
                
                <div class="checkbox-group">
                    <input type="checkbox" id="semanticSearch" checked>
                    <label for="semanticSearch">Semantic Search</label>
                </div>
            </div>
        </div>
        
        <div class="results-section">
            <div class="results-header">
                <h2>Search Results</h2>
                <span id="resultsCount">0 workflows found</span>
            </div>
            
            <div id="resultsContainer">
                <div class="loading">
                    <p>🔍 Enter a search term to find workflows</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let currentResults = [];
        
        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
            loadCategories();

            // Add enter key support for search
            document.getElementById('searchInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchWorkflows();
                }
            });

            // Load initial data
            searchWorkflows();
        });

        // Load statistics
        async function loadStats() {
            try {
                const response = await fetch('/api/stats');
                const data = await response.json();

                if (data.status === 'success') {
                    displayStats(data.stats);
                }
            } catch (error) {
                console.error('Error loading stats:', error);
            }
        }

        // Display statistics
        function displayStats(stats) {
            const statsSection = document.getElementById('statsSection');
            statsSection.innerHTML = `
                <div class="stat-card">
                    <div class="stat-number">${stats.total_workflows || 0}</div>
                    <div class="stat-label">Total Workflows</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${stats.free_workflows || 0}</div>
                    <div class="stat-label">Free Workflows</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${Object.keys(stats.categories || {}).length}</div>
                    <div class="stat-label">Categories</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${stats.vector_store_size || 0}</div>
                    <div class="stat-label">Indexed for Search</div>
                </div>
            `;
        }

        // Load categories
        async function loadCategories() {
            try {
                const response = await fetch('/api/categories');
                const data = await response.json();

                if (data.status === 'success') {
                    const categoryFilter = document.getElementById('categoryFilter');
                    data.categories.forEach(category => {
                        if (category) {
                            const option = document.createElement('option');
                            option.value = category;
                            option.textContent = category;
                            categoryFilter.appendChild(option);
                        }
                    });
                }
            } catch (error) {
                console.error('Error loading categories:', error);
            }
        }

        // Search workflows
        async function searchWorkflows() {
            const query = document.getElementById('searchInput').value;
            const category = document.getElementById('categoryFilter').value;
            const typeFilter = document.getElementById('typeFilter').value;
            const semantic = document.getElementById('semanticSearch').checked;

            const resultsContainer = document.getElementById('resultsContainer');
            resultsContainer.innerHTML = '<div class="loading"><p>🔍 Searching workflows...</p></div>';

            try {
                const params = new URLSearchParams();
                if (query) params.append('q', query);
                if (category) params.append('category', category);
                if (typeFilter) params.append('is_free', typeFilter);
                params.append('semantic', semantic);
                params.append('limit', '20');

                const response = await fetch(`/api/workflows/search?${params}`);
                const data = await response.json();

                if (data.status === 'success') {
                    currentResults = data.results;
                    displayResults(data.results, data.count);
                } else {
                    showError('Search failed: ' + data.error);
                }
            } catch (error) {
                showError('Network error: ' + error.message);
            }
        }

        // Display search results
        function displayResults(results, count) {
            const resultsContainer = document.getElementById('resultsContainer');
            const resultsCount = document.getElementById('resultsCount');

            resultsCount.textContent = `${count} workflow${count !== 1 ? 's' : ''} found`;

            if (results.length === 0) {
                resultsContainer.innerHTML = `
                    <div class="loading">
                        <p>😔 No workflows found. Try different search terms or filters.</p>
                    </div>
                `;
                return;
            }

            resultsContainer.innerHTML = results.map(workflow => `
                <div class="workflow-card" onclick="showWorkflowDetails('${workflow.id}')">
                    <div class="workflow-title">${escapeHtml(workflow.title || 'Untitled Workflow')}</div>
                    <div class="workflow-description">${escapeHtml(workflow.description || 'No description available')}</div>
                    <div class="workflow-meta">
                        <div class="meta-item">
                            <span>👤 ${escapeHtml(workflow.author || 'Unknown')}</span>
                        </div>
                        <div class="meta-item">
                            <span>👁️ ${workflow.view_count || 0} views</span>
                        </div>
                        <div class="meta-item">
                            <span>🔗 ${workflow.node_count || 0} nodes</span>
                        </div>
                        ${workflow.category ? `<div class="meta-item"><span class="badge">${escapeHtml(workflow.category)}</span></div>` : ''}
                        <div class="meta-item">
                            <span class="badge ${workflow.is_free ? 'free' : 'paid'}">${workflow.is_free ? 'Free' : 'Paid'}</span>
                        </div>
                        ${workflow.similarity_score ? `<div class="meta-item"><span class="similarity-score">Similarity: ${(workflow.similarity_score * 100).toFixed(1)}%</span></div>` : ''}
                    </div>
                </div>
            `).join('');
        }

        // Show workflow details
        async function showWorkflowDetails(workflowId) {
            try {
                const response = await fetch(`/api/workflows/${workflowId}`);
                const data = await response.json();

                if (data.status === 'success') {
                    const workflow = data.workflow;
                    alert(`Workflow: ${workflow.title}\n\nDescription: ${workflow.description}\n\nAuthor: ${workflow.author}\nViews: ${workflow.view_count}\nNodes: ${workflow.node_count}\nCategory: ${workflow.category}\nType: ${workflow.is_free ? 'Free' : 'Paid'}\n\nURL: ${workflow.url}`);
                }
            } catch (error) {
                console.error('Error loading workflow details:', error);
            }
        }

        // Trigger manual update
        async function triggerUpdate() {
            const updateBtn = document.getElementById('updateBtn');
            const updateStatus = document.getElementById('updateStatus');

            updateBtn.disabled = true;
            updateBtn.textContent = '🔄 Updating...';
            updateStatus.textContent = 'Starting update...';

            try {
                const response = await fetch('/api/update/trigger', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ max_workflows: 50 })
                });

                const data = await response.json();

                if (data.status === 'success') {
                    const result = data.update_result;
                    updateStatus.textContent = `✅ Update completed: ${result.new_count} new, ${result.updated_count} updated`;

                    // Reload stats and results
                    setTimeout(() => {
                        loadStats();
                        searchWorkflows();
                    }, 1000);
                } else {
                    updateStatus.textContent = `❌ Update failed: ${data.error}`;
                }
            } catch (error) {
                updateStatus.textContent = `❌ Network error: ${error.message}`;
            } finally {
                updateBtn.disabled = false;
                updateBtn.textContent = '🔄 Update Workflows';

                // Clear status after 10 seconds
                setTimeout(() => {
                    updateStatus.textContent = '';
                }, 10000);
            }
        }

        // Show error message
        function showError(message) {
            const resultsContainer = document.getElementById('resultsContainer');
            resultsContainer.innerHTML = `
                <div class="error">
                    <strong>Error:</strong> ${escapeHtml(message)}
                </div>
            `;
        }

        // Escape HTML to prevent XSS
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
    </script>
</body>
</html>
