# AgentChef Workflow Knowledge Base Setup Script
# This script sets up the complete system for extracting and managing workflow data

Write-Host "🚀 Setting up AgentChef Workflow Knowledge Base System" -ForegroundColor Green
Write-Host "==================================================" -ForegroundColor Green

# Function to check if command exists
function Test-Command {
    param($Command)
    try {
        Get-Command $Command -ErrorAction Stop
        return $true
    } catch {
        return $false
    }
}

# Check if Python is installed
if (-not (Test-Command "python")) {
    Write-Host "❌ Python is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Python 3.8+ from https://python.org" -ForegroundColor Yellow
    exit 1
} else {
    Write-Host "✅ Python found" -ForegroundColor Green
}

# Check if pip is available
if (-not (Test-Command "pip")) {
    Write-Host "❌ pip is not available" -ForegroundColor Red
    exit 1
} else {
    Write-Host "✅ pip found" -ForegroundColor Green
}

# Install Python dependencies
Write-Host "🐍 Installing Python dependencies..." -ForegroundColor Blue
$packages = @(
    "selenium",
    "webdriver-manager", 
    "numpy",
    "flask",
    "flask-cors",
    "schedule",
    "beautifulsoup4",
    "lxml"
)

foreach ($package in $packages) {
    Write-Host "Installing $package..." -ForegroundColor Yellow
    pip install $package
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Failed to install $package" -ForegroundColor Red
        exit 1
    }
}

Write-Host "✅ All Python packages installed successfully" -ForegroundColor Green

# Set up directory structure
Write-Host "📁 Setting up directory structure..." -ForegroundColor Blue
$directories = @("logs", "backups")
foreach ($dir in $directories) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "Created directory: $dir" -ForegroundColor Yellow
    }
}

# Set up Flask app virtual environment
Write-Host "🌐 Setting up Flask application..." -ForegroundColor Blue
if (-not (Test-Path "workflow-manager/venv")) {
    Set-Location "workflow-manager"
    python -m venv venv
    & "venv/Scripts/Activate.ps1"
    pip install numpy selenium webdriver-manager schedule flask flask-cors
    Set-Location ".."
    Write-Host "✅ Flask virtual environment created" -ForegroundColor Green
} else {
    Write-Host "✅ Flask virtual environment already exists" -ForegroundColor Green
}

# Create backup script
Write-Host "💾 Creating backup script..." -ForegroundColor Blue
$backupScript = @'
# Backup script for AgentChef Workflow Knowledge Base

$BackupDir = "backups"
$Timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$BackupFile = "$BackupDir/agentchef_backup_$Timestamp.zip"

Write-Host "Creating backup: $BackupFile"

# Create backup directory if it doesn't exist
if (-not (Test-Path $BackupDir)) {
    New-Item -ItemType Directory -Path $BackupDir -Force | Out-Null
}

# Files to backup
$FilesToBackup = @(
    "workflows.db",
    "vector_store.pkl", 
    "workflows_data.json",
    "last_sync.json",
    "update_stats.json"
)

# Add log files
$LogFiles = Get-ChildItem -Path "." -Filter "*.log" -ErrorAction SilentlyContinue
$FilesToBackup += $LogFiles.Name

# Create backup
$ExistingFiles = $FilesToBackup | Where-Object { Test-Path $_ }
if ($ExistingFiles.Count -gt 0) {
    Compress-Archive -Path $ExistingFiles -DestinationPath $BackupFile -Force
    Write-Host "Backup created successfully: $BackupFile"
    
    # Keep only last 10 backups
    $OldBackups = Get-ChildItem -Path $BackupDir -Filter "agentchef_backup_*.zip" | 
                  Sort-Object LastWriteTime -Descending | 
                  Select-Object -Skip 10
    $OldBackups | Remove-Item -Force
} else {
    Write-Host "No files to backup found"
}
'@

$backupScript | Out-File -FilePath "backup.ps1" -Encoding UTF8
Write-Host "✅ Backup script created" -ForegroundColor Green

# Create run script
Write-Host "🏃 Creating run script..." -ForegroundColor Blue
$runScript = @'
# Run script for AgentChef Workflow Knowledge Base

Write-Host "🚀 Starting AgentChef Workflow Knowledge Base System" -ForegroundColor Green

# Function to check if port is in use
function Test-Port {
    param($Port)
    try {
        $listener = [System.Net.Sockets.TcpListener]::new([System.Net.IPAddress]::Any, $Port)
        $listener.Start()
        $listener.Stop()
        return $false
    } catch {
        return $true
    }
}

# Check and stop existing processes
if (Test-Port 5000) {
    Write-Host "⚠️  Port 5000 is already in use" -ForegroundColor Yellow
    Write-Host "Stopping existing processes..." -ForegroundColor Yellow
    Get-Process -Name "python" -ErrorAction SilentlyContinue | 
        Where-Object { $_.CommandLine -like "*main.py*" } | 
        Stop-Process -Force
    Start-Sleep -Seconds 2
}

# Start the Flask application
Write-Host "🌐 Starting web interface..." -ForegroundColor Blue
Set-Location "workflow-manager"
& "venv/Scripts/Activate.ps1"
Start-Process -FilePath "python" -ArgumentList "src/main.py" -WindowStyle Hidden
Set-Location ".."

# Wait for Flask to start
Write-Host "⏳ Waiting for web interface to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

# Check if Flask is running
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5000/api/health" -TimeoutSec 10 -ErrorAction Stop
    Write-Host "✅ Web interface started successfully!" -ForegroundColor Green
    Write-Host "🌐 Access the interface at: http://localhost:5000" -ForegroundColor Cyan
    Write-Host "📊 API health check: http://localhost:5000/api/health" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "📋 Available commands:" -ForegroundColor Blue
    Write-Host "  .\extract.ps1     - Extract new workflow data" -ForegroundColor White
    Write-Host "  .\update.ps1      - Run incremental update" -ForegroundColor White
    Write-Host "  .\backup.ps1      - Create backup" -ForegroundColor White
    Write-Host "  .\stop.ps1        - Stop all services" -ForegroundColor White
} catch {
    Write-Host "❌ Failed to start web interface" -ForegroundColor Red
    Write-Host "Check logs for details" -ForegroundColor Yellow
}
'@

$runScript | Out-File -FilePath "run.ps1" -Encoding UTF8
Write-Host "✅ Run script created" -ForegroundColor Green

# Create extract script
Write-Host "📥 Creating extract script..." -ForegroundColor Blue
$extractScript = @'
# Extract workflow data from AgentChef

Write-Host "📥 Extracting workflow data from AgentChef..." -ForegroundColor Blue

# Run extraction
python agentchef_extractor.py

# Setup database if extraction was successful
if ($LASTEXITCODE -eq 0) {
    Write-Host "🗄️ Setting up knowledge base..." -ForegroundColor Blue
    python database_setup.py
    Write-Host "✅ Extraction and setup completed!" -ForegroundColor Green
} else {
    Write-Host "❌ Extraction failed" -ForegroundColor Red
    exit 1
}
'@

$extractScript | Out-File -FilePath "extract.ps1" -Encoding UTF8
Write-Host "✅ Extract script created" -ForegroundColor Green

# Create update script
Write-Host "🔄 Creating update script..." -ForegroundColor Blue
$updateScript = @'
# Run incremental update

Write-Host "🔄 Running incremental update..." -ForegroundColor Blue

# Create backup before update
Write-Host "Creating backup before update..." -ForegroundColor Yellow
& ".\backup.ps1"

# Run incremental update
python incremental_updater.py

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Incremental update completed!" -ForegroundColor Green
} else {
    Write-Host "❌ Update failed" -ForegroundColor Red
    exit 1
}
'@

$updateScript | Out-File -FilePath "update.ps1" -Encoding UTF8
Write-Host "✅ Update script created" -ForegroundColor Green

# Create stop script
Write-Host "🛑 Creating stop script..." -ForegroundColor Blue
$stopScript = @'
# Stop all AgentChef services

Write-Host "🛑 Stopping AgentChef Workflow Knowledge Base System..." -ForegroundColor Yellow

# Stop Flask application
Get-Process -Name "python" -ErrorAction SilentlyContinue | 
    Where-Object { $_.CommandLine -like "*main.py*" } | 
    Stop-Process -Force

# Stop any running extraction processes
Get-Process -Name "python" -ErrorAction SilentlyContinue | 
    Where-Object { $_.CommandLine -like "*agentchef_extractor.py*" } | 
    Stop-Process -Force

Get-Process -Name "python" -ErrorAction SilentlyContinue | 
    Where-Object { $_.CommandLine -like "*incremental_updater.py*" } | 
    Stop-Process -Force

# Stop Chrome processes
Get-Process -Name "chrome" -ErrorAction SilentlyContinue | 
    Where-Object { $_.CommandLine -like "*agentchef*" } | 
    Stop-Process -Force

Write-Host "✅ All services stopped" -ForegroundColor Green
'@

$stopScript | Out-File -FilePath "stop.ps1" -Encoding UTF8
Write-Host "✅ Stop script created" -ForegroundColor Green

# Create logs directory
if (-not (Test-Path "logs")) {
    New-Item -ItemType Directory -Path "logs" -Force | Out-Null
}

Write-Host ""
Write-Host "🎯 Setup completed successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Next steps:" -ForegroundColor Blue
Write-Host "1. Run initial extraction:  .\extract.ps1" -ForegroundColor White
Write-Host "2. Start the web interface: .\run.ps1" -ForegroundColor White
Write-Host "3. Access the interface at:  http://localhost:5000" -ForegroundColor White
Write-Host ""
Write-Host "📁 Available scripts:" -ForegroundColor Blue
Write-Host "  .\run.ps1      - Start the web interface" -ForegroundColor White
Write-Host "  .\extract.ps1  - Extract workflow data" -ForegroundColor White
Write-Host "  .\update.ps1   - Run incremental update" -ForegroundColor White
Write-Host "  .\backup.ps1   - Create backup" -ForegroundColor White
Write-Host "  .\stop.ps1     - Stop all services" -ForegroundColor White
Write-Host ""

# Ask if user wants to run initial extraction
$response = Read-Host "🤔 Would you like to run initial extraction now? (y/N)"
if ($response -eq "y" -or $response -eq "Y") {
    Write-Host "🚀 Running initial extraction..." -ForegroundColor Green
    & ".\extract.ps1"
    
    $response2 = Read-Host "🌐 Would you like to start the web interface now? (y/N)"
    if ($response2 -eq "y" -or $response2 -eq "Y") {
        & ".\run.ps1"
    }
}

Write-Host ""
Write-Host "🎉 AgentChef Workflow Knowledge Base is ready!" -ForegroundColor Green
