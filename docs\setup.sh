#!/bin/bash

# AgentChef Workflow Knowledge Base Setup Script
# This script sets up the complete system for extracting and managing workflow data

set -e  # Exit on any error

echo "🚀 Setting up AgentChef Workflow Knowledge Base System"
echo "=================================================="

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   echo "❌ This script should not be run as root"
   exit 1
fi

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Install system dependencies
echo "📦 Installing system dependencies..."

# Update package list
sudo apt-get update

# Install Chrome if not present
if ! command_exists google-chrome; then
    echo "Installing Google Chrome..."
    wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | sudo apt-key add -
    echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" | sudo tee /etc/apt/sources.list.d/google-chrome.list
    sudo apt-get update
    sudo apt-get install -y google-chrome-stable
else
    echo "✅ Google Chrome already installed"
fi

# Install Python dependencies
echo "🐍 Installing Python dependencies..."
pip3 install --user selenium webdriver-manager numpy flask flask-cors schedule beautifulsoup4 lxml

# Set up directory structure
echo "📁 Setting up directory structure..."
mkdir -p logs
mkdir -p backups

# Make scripts executable
chmod +x agentchef_extractor.py
chmod +x database_setup.py
chmod +x incremental_updater.py

# Set up Flask app dependencies
echo "🌐 Setting up Flask application..."
cd workflow-manager
source venv/bin/activate
pip install numpy selenium webdriver-manager schedule flask-cors
cd ..

# Create systemd service file for automatic startup (optional)
echo "⚙️ Creating systemd service file..."
cat > agentchef-workflow-manager.service << EOF
[Unit]
Description=AgentChef Workflow Manager
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=$(pwd)/workflow-manager
Environment=PATH=$(pwd)/workflow-manager/venv/bin
ExecStart=$(pwd)/workflow-manager/venv/bin/python src/main.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

echo "📋 Service file created: agentchef-workflow-manager.service"
echo "To install as system service, run:"
echo "  sudo cp agentchef-workflow-manager.service /etc/systemd/system/"
echo "  sudo systemctl enable agentchef-workflow-manager"
echo "  sudo systemctl start agentchef-workflow-manager"

# Create backup script
echo "💾 Creating backup script..."
cat > backup.sh << 'EOF'
#!/bin/bash
# Backup script for AgentChef Workflow Knowledge Base

BACKUP_DIR="backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="$BACKUP_DIR/agentchef_backup_$TIMESTAMP.tar.gz"

echo "Creating backup: $BACKUP_FILE"

# Create backup directory if it doesn't exist
mkdir -p $BACKUP_DIR

# Create backup
tar -czf $BACKUP_FILE \
    workflows.db \
    vector_store.pkl \
    workflows_data.json \
    last_sync.json \
    update_stats.json \
    *.log \
    2>/dev/null || true

echo "Backup created successfully: $BACKUP_FILE"

# Keep only last 10 backups
ls -t $BACKUP_DIR/agentchef_backup_*.tar.gz | tail -n +11 | xargs rm -f 2>/dev/null || true
EOF

chmod +x backup.sh

# Create run script
echo "🏃 Creating run script..."
cat > run.sh << 'EOF'
#!/bin/bash
# Run script for AgentChef Workflow Knowledge Base

echo "🚀 Starting AgentChef Workflow Knowledge Base System"

# Function to check if port is in use
check_port() {
    if lsof -Pi :5000 -sTCP:LISTEN -t >/dev/null ; then
        echo "⚠️  Port 5000 is already in use"
        echo "Stopping existing process..."
        pkill -f "python.*main.py" || true
        sleep 2
    fi
}

# Check and stop existing processes
check_port

# Start the Flask application
echo "🌐 Starting web interface..."
cd workflow-manager
source venv/bin/activate
nohup python src/main.py > ../logs/flask.log 2>&1 &
FLASK_PID=$!
cd ..

# Wait for Flask to start
echo "⏳ Waiting for web interface to start..."
sleep 5

# Check if Flask is running
if curl -s http://localhost:5000/api/health > /dev/null; then
    echo "✅ Web interface started successfully!"
    echo "🌐 Access the interface at: http://localhost:5000"
    echo "📊 API health check: http://localhost:5000/api/health"
    echo "📝 Process ID: $FLASK_PID"
    echo ""
    echo "📋 Available commands:"
    echo "  ./extract.sh     - Extract new workflow data"
    echo "  ./update.sh      - Run incremental update"
    echo "  ./backup.sh      - Create backup"
    echo "  ./stop.sh        - Stop all services"
else
    echo "❌ Failed to start web interface"
    echo "Check logs/flask.log for details"
    exit 1
fi
EOF

chmod +x run.sh

# Create extract script
echo "📥 Creating extract script..."
cat > extract.sh << 'EOF'
#!/bin/bash
# Extract workflow data from AgentChef

echo "📥 Extracting workflow data from AgentChef..."

# Run extraction
python3 agentchef_extractor.py

# Setup database if extraction was successful
if [ $? -eq 0 ]; then
    echo "🗄️ Setting up knowledge base..."
    python3 database_setup.py
    echo "✅ Extraction and setup completed!"
else
    echo "❌ Extraction failed"
    exit 1
fi
EOF

chmod +x extract.sh

# Create update script
echo "🔄 Creating update script..."
cat > update.sh << 'EOF'
#!/bin/bash
# Run incremental update

echo "🔄 Running incremental update..."

# Create backup before update
./backup.sh

# Run incremental update
python3 incremental_updater.py

if [ $? -eq 0 ]; then
    echo "✅ Incremental update completed!"
else
    echo "❌ Update failed"
    exit 1
fi
EOF

chmod +x update.sh

# Create stop script
echo "🛑 Creating stop script..."
cat > stop.sh << 'EOF'
#!/bin/bash
# Stop all AgentChef services

echo "🛑 Stopping AgentChef Workflow Knowledge Base System..."

# Stop Flask application
pkill -f "python.*main.py" || true

# Stop any running extraction processes
pkill -f "agentchef_extractor.py" || true
pkill -f "incremental_updater.py" || true

# Stop Chrome processes
pkill -f "chrome.*agentchef" || true

echo "✅ All services stopped"
EOF

chmod +x stop.sh

# Create logs directory
mkdir -p logs

# Initial extraction (optional)
echo ""
echo "🎯 Setup completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Run initial extraction:  ./extract.sh"
echo "2. Start the web interface: ./run.sh"
echo "3. Access the interface at:  http://localhost:5000"
echo ""
echo "📁 Available scripts:"
echo "  ./run.sh      - Start the web interface"
echo "  ./extract.sh  - Extract workflow data"
echo "  ./update.sh   - Run incremental update"
echo "  ./backup.sh   - Create backup"
echo "  ./stop.sh     - Stop all services"
echo ""
echo "📖 For detailed documentation, see README.md"
echo ""

# Ask if user wants to run initial extraction
read -p "🤔 Would you like to run initial extraction now? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🚀 Running initial extraction..."
    ./extract.sh
    
    echo ""
    read -p "🌐 Would you like to start the web interface now? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        ./run.sh
    fi
fi

echo ""
echo "🎉 AgentChef Workflow Knowledge Base is ready!"
echo "📚 Check README.md for detailed usage instructions"

