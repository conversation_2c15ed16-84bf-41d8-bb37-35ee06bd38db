#!/usr/bin/env python3
"""
AgentChef Extractor with Separate Chrome Instance
Creates a completely separate Chrome instance that won't interfere with your existing work
"""

import time
import json
import os
import tempfile
from pathlib import Path
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

class SafeChromeExtractor:
    """Chrome extractor that uses a separate profile to avoid interfering with existing sessions"""
    
    def __init__(self, download_dir="agentchef_safe_extraction"):
        self.download_dir = Path(download_dir)
        self.download_dir.mkdir(exist_ok=True)
        (self.download_dir / "json_files").mkdir(exist_ok=True)
        (self.download_dir / "metadata").mkdir(exist_ok=True)
        (self.download_dir / "logs").mkdir(exist_ok=True)
        
        self.driver = None
        self.temp_profile_dir = None
        self.stats = {
            'total_processed': 0,
            'json_downloaded': 0,
            'metadata_only': 0,
            'paid_skipped': 0,
            'errors': 0
        }
    
    def setup_separate_chrome(self, headless=False):
        """Setup Chrome with a completely separate temporary profile"""
        chrome_options = Options()
        
        # Create temporary profile directory
        self.temp_profile_dir = tempfile.mkdtemp(prefix="agentchef_chrome_")
        print(f"📁 Using temporary Chrome profile: {self.temp_profile_dir}")
        
        # Configure Chrome to use temporary profile
        chrome_options.add_argument(f"--user-data-dir={self.temp_profile_dir}")
        chrome_options.add_argument("--profile-directory=Default")
        
        # Download settings
        download_path = str(self.download_dir.absolute())
        prefs = {
            "download.default_directory": download_path,
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True,
            "profile.default_content_settings.popups": 0,
            "profile.default_content_setting_values.automatic_downloads": 1
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        # Other settings
        if headless:
            chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--allow-running-insecure-content')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Ensure this is a separate instance
        chrome_options.add_argument('--no-first-run')
        chrome_options.add_argument('--no-default-browser-check')
        chrome_options.add_argument('--disable-default-apps')
        
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            print("✅ Separate Chrome instance created successfully")
            return True
        except Exception as e:
            print(f"❌ Failed to create Chrome instance: {e}")
            return False
    
    def manual_login_flow(self):
        """Handle manual login in the separate Chrome instance"""
        print("\n🔐 MANUAL LOGIN REQUIRED")
        print("=" * 50)
        print("A separate Chrome window has opened for AgentChef extraction.")
        print("This will NOT affect your existing Chrome sessions.")
        print()
        print("Please:")
        print("1. Look for the new Chrome window (separate from your work)")
        print("2. Log in to AgentChef in this new window")
        print("3. Come back here and press Enter when logged in")
        print()
        print("⚠️ Do NOT close this new Chrome window!")
        print("=" * 50)
        
        # Navigate to AgentChef
        self.driver.get("https://agentchef.vercel.app")
        time.sleep(3)
        
        input("Press Enter AFTER you have logged in to AgentChef in the separate window...")
        
        # Verify login
        test_url = "https://agentchef.vercel.app/workflow/ab2d1ec2-713b-4559-99d5-5aa8b4d4ef71"
        self.driver.get(test_url)
        time.sleep(3)
        
        current_url = self.driver.current_url
        if "login" in current_url.lower() or "auth" in current_url.lower():
            print("❌ Login verification failed - still redirected to login")
            return False
        else:
            print("✅ Login verified successfully!")
            return True
    
    def extract_workflow_json(self, workflow_id):
        """Extract JSON for a specific workflow"""
        workflow_url = f"https://agentchef.vercel.app/workflow/{workflow_id}"
        print(f"🌐 Processing: {workflow_url}")
        
        try:
            self.driver.get(workflow_url)
            time.sleep(5)
            
            # Extract basic metadata
            title = "Unknown"
            description = "No description"
            
            try:
                title_element = self.driver.find_element(By.TAG_NAME, "h1")
                title = title_element.text.strip()
            except:
                pass
            
            try:
                # Look for description
                desc_elements = self.driver.find_elements(By.CSS_SELECTOR, "p, div")
                for elem in desc_elements:
                    text = elem.text.strip()
                    if len(text) > 50 and len(text) < 500:  # Reasonable description length
                        description = text
                        break
            except:
                pass
            
            print(f"   📋 Title: {title}")
            print(f"   📄 Description: {description[:100]}...")
            
            # Look for Copy Workflow JSON button
            copy_button = None
            buttons = self.driver.find_elements(By.TAG_NAME, "button")
            
            for button in buttons:
                try:
                    button_text = button.text.strip()
                    if "Copy Workflow JSON" in button_text or "Copy JSON" in button_text:
                        copy_button = button
                        print(f"   ✅ Found copy button: '{button_text}'")
                        break
                except:
                    continue
            
            if not copy_button:
                print("   ❌ No 'Copy Workflow JSON' button found")
                self.stats['metadata_only'] += 1
                return {
                    'id': workflow_id,
                    'title': title,
                    'description': description,
                    'has_json': False
                }
            
            # Click copy button and extract JSON
            print("   🖱️ Clicking copy button...")
            try:
                copy_button.click()
            except:
                self.driver.execute_script("arguments[0].click();", copy_button)
            
            time.sleep(2)
            
            # Extract clipboard content using textarea method
            print("   📋 Extracting clipboard content...")
            self.driver.execute_script("""
                var textarea = document.createElement('textarea');
                textarea.id = 'agentchef-clipboard-extractor';
                textarea.style.position = 'fixed';
                textarea.style.top = '0';
                textarea.style.left = '0';
                textarea.style.width = '200px';
                textarea.style.height = '200px';
                textarea.style.opacity = '1';
                textarea.style.zIndex = '9999';
                textarea.style.border = '2px solid red';
                document.body.appendChild(textarea);
                textarea.focus();
                textarea.select();
            """)
            
            time.sleep(1)
            
            # Paste content
            textarea = self.driver.find_element(By.ID, "agentchef-clipboard-extractor")
            textarea.send_keys(Keys.CONTROL + "v")
            time.sleep(2)
            
            content = textarea.get_attribute("value")
            
            # Clean up
            self.driver.execute_script("document.getElementById('agentchef-clipboard-extractor').remove();")
            
            if content and content.strip():
                print(f"   ✅ Clipboard content extracted! Length: {len(content)} characters")
                
                try:
                    json_data = json.loads(content)
                    print("   ✅ Content is valid JSON!")
                    
                    # Save JSON file
                    json_file = self.download_dir / "json_files" / f"{workflow_id}.json"
                    with open(json_file, 'w', encoding='utf-8') as f:
                        json.dump(json_data, f, indent=2, ensure_ascii=False)
                    
                    print(f"   💾 Saved JSON to: {json_file}")
                    self.stats['json_downloaded'] += 1
                    
                    return {
                        'id': workflow_id,
                        'title': title,
                        'description': description,
                        'has_json': True,
                        'json_file': str(json_file)
                    }
                    
                except json.JSONDecodeError:
                    print("   ❌ Clipboard content is not valid JSON")
                    print(f"   📄 Content preview: {content[:200]}...")
                    self.stats['metadata_only'] += 1
            else:
                print("   ❌ No content extracted from clipboard")
                self.stats['metadata_only'] += 1
            
            return {
                'id': workflow_id,
                'title': title,
                'description': description,
                'has_json': False
            }
            
        except Exception as e:
            print(f"   ❌ Error processing workflow: {e}")
            self.stats['errors'] += 1
            return None
    
    def run_extraction(self, max_workflows=10):
        """Run the complete extraction process"""
        print("🚀 Starting Safe AgentChef Extraction")
        print("=" * 50)
        print(f"📊 Target: {max_workflows} workflows")
        print(f"📁 Output: {self.download_dir}")
        print("🔒 Using separate Chrome instance (won't affect your work)")
        print()
        
        try:
            # Setup separate Chrome
            if not self.setup_separate_chrome(headless=False):
                return False
            
            # Manual login
            if not self.manual_login_flow():
                return False
            
            # Get workflow IDs from browse page
            print("\n🔍 Loading browse page...")
            self.driver.get("https://agentchef.vercel.app/browse")
            time.sleep(5)
            
            # Extract workflow IDs (simplified)
            workflow_links = self.driver.find_elements(By.CSS_SELECTOR, 'a[href*="/workflow/"]')
            workflow_ids = []
            
            for link in workflow_links:
                href = link.get_attribute('href')
                if href and '/workflow/' in href:
                    workflow_id = href.split('/workflow/')[-1]
                    if workflow_id and workflow_id not in workflow_ids:
                        workflow_ids.append(workflow_id)
            
            if not workflow_ids:
                print("❌ No workflow IDs found")
                return False
            
            workflow_ids = workflow_ids[:max_workflows]
            print(f"✅ Found {len(workflow_ids)} workflows to process")
            
            # Extract each workflow
            results = []
            for i, workflow_id in enumerate(workflow_ids):
                print(f"\n🔄 Processing {i+1}/{len(workflow_ids)}: {workflow_id}")
                result = self.extract_workflow_json(workflow_id)
                if result:
                    results.append(result)
                    self.stats['total_processed'] += 1
                
                time.sleep(2)  # Be respectful
            
            # Save summary
            summary_file = self.download_dir / "extraction_summary.json"
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'stats': self.stats,
                    'workflows': results,
                    'extracted_at': time.strftime('%Y-%m-%d %H:%M:%S')
                }, f, indent=2)
            
            # Print summary
            print("\n" + "="*50)
            print("📊 EXTRACTION SUMMARY")
            print("="*50)
            print(f"Total processed: {self.stats['total_processed']}")
            print(f"JSON downloaded: {self.stats['json_downloaded']}")
            print(f"Metadata only: {self.stats['metadata_only']}")
            print(f"Errors: {self.stats['errors']}")
            
            if self.stats['total_processed'] > 0:
                success_rate = (self.stats['json_downloaded'] / self.stats['total_processed']) * 100
                print(f"JSON success rate: {success_rate:.1f}%")
            
            print(f"\n📁 Files saved to: {self.download_dir}")
            print("="*50)
            
            return True
            
        except Exception as e:
            print(f"❌ Extraction failed: {e}")
            return False
        finally:
            self.cleanup()
    
    def cleanup(self):
        """Clean up resources"""
        if self.driver:
            print("🔒 Closing separate Chrome instance...")
            self.driver.quit()
        
        # Clean up temporary profile directory
        if self.temp_profile_dir and os.path.exists(self.temp_profile_dir):
            try:
                import shutil
                shutil.rmtree(self.temp_profile_dir)
                print("🗑️ Cleaned up temporary profile")
            except:
                print(f"⚠️ Could not clean up temporary profile: {self.temp_profile_dir}")

def main():
    print("🔒 Safe AgentChef Extractor")
    print("=" * 40)
    print("This creates a separate Chrome instance that won't interfere with your work.")
    print()
    
    max_workflows = input("How many workflows to extract? (default: 5): ").strip()
    if not max_workflows:
        max_workflows = 5
    else:
        try:
            max_workflows = int(max_workflows)
        except ValueError:
            max_workflows = 5
    
    extractor = SafeChromeExtractor()
    success = extractor.run_extraction(max_workflows)
    
    if success:
        print("🎉 Extraction completed successfully!")
    else:
        print("❌ Extraction failed")

if __name__ == "__main__":
    main()
