{"id": "ab2d1ec2-713b-4559-99d5-5aa8b4d4ef71", "title": "AI agent chat", "description": "This workflow employs OpenAI's language models and SerpAPI to create a responsive, intelligent conversational agent. It comes equipped with manual chat triggers and memory buffer capabilities to ensure seamless interactions.", "category": "", "author": "", "author_verified": false, "view_count": 0, "node_count": 0, "is_free": true, "created_date": null, "node_types": [], "url": "https://agentchef.vercel.app/workflow/ab2d1ec2-713b-4559-99d5-5aa8b4d4ef71", "has_json": true, "extracted_at": "2025-06-30T13:23:08.238147"}