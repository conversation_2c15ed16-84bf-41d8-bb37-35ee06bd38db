{"id": "3da8e99e-bf72-42f8-a4ca-eb9e9737baca", "title": "Scrape and summarize webpages with AI", "description": "This workflow integrates both web scraping and NLP functionalities. It uses HTML parsing to extract links, HTTP requests to fetch essay content, and AI-based summarization using GPT-4o. It's an excellent example of an end-to-end automated task that is not only efficient but also provides real value by summarizing valuable content.", "category": "", "author": "summarizing valuable content.", "author_verified": false, "view_count": 0, "node_count": 0, "is_free": true, "created_date": null, "node_types": [], "url": "https://agentchef.vercel.app/workflow/3da8e99e-bf72-42f8-a4ca-eb9e9737baca", "has_json": false, "extracted_at": "2025-06-30T13:13:27.416695"}