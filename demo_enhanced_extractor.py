#!/usr/bin/env python3
"""
Demonstration script for the enhanced AgentChef extractor
This script shows how to use the enhanced features safely
"""

import os
import json
import time
from pathlib import Path
from agentchef_extractor import AgentChefExtractor
from database_setup import WorkflowKnowledgeBase

def demo_basic_extraction():
    """Demonstrate basic extraction with a small number of workflows"""
    print("🚀 Demo: Basic Enhanced Extraction")
    print("=" * 50)
    
    # Initialize extractor with demo directory
    demo_dir = "demo_extraction"
    extractor = AgentChefExtractor(download_dir=demo_dir)
    
    print(f"📁 Download directory: {demo_dir}")
    print("🎯 Target: 3 workflows (small demo)")
    print("📄 JSON extraction: Enabled")
    print("🔐 Authentication: Interactive mode")
    print()
    
    # Ask user if they want to proceed
    response = input("Would you like to run the demo extraction? (y/N): ")
    if response.lower() != 'y':
        print("Demo cancelled.")
        return
    
    try:
        # Run extraction with small number for demo
        print("\n🔄 Starting extraction...")
        extractor.run_extraction(
            max_workflows=3,  # Small number for demo
            interactive_auth=True,
            extract_json=True
        )
        
        # Show results
        print("\n📊 Demo Results:")
        demo_path = Path(demo_dir)
        
        if demo_path.exists():
            json_files = list((demo_path / "json_files").glob("*.json"))
            metadata_files = list((demo_path / "metadata").glob("*.json"))
            log_files = list((demo_path / "logs").glob("*.json"))
            
            print(f"✅ JSON files downloaded: {len(json_files)}")
            print(f"✅ Metadata files created: {len(metadata_files)}")
            print(f"✅ Log files generated: {len(log_files)}")
            
            # Show file structure
            print(f"\n📁 Files created in {demo_dir}:")
            for file in demo_path.rglob("*"):
                if file.is_file():
                    relative_path = file.relative_to(demo_path)
                    print(f"   {relative_path}")
        
        print("\n✅ Demo extraction completed!")
        
    except Exception as e:
        print(f"❌ Demo extraction failed: {e}")

def demo_metadata_only():
    """Demonstrate metadata-only extraction"""
    print("\n🚀 Demo: Metadata-Only Extraction")
    print("=" * 50)
    
    demo_dir = "demo_metadata_only"
    extractor = AgentChefExtractor(download_dir=demo_dir)
    
    print(f"📁 Download directory: {demo_dir}")
    print("🎯 Target: 5 workflows")
    print("📄 JSON extraction: Disabled (metadata only)")
    print("🔐 Authentication: Not required for metadata")
    print()
    
    response = input("Would you like to run metadata-only extraction? (y/N): ")
    if response.lower() != 'y':
        print("Demo cancelled.")
        return
    
    try:
        print("\n🔄 Starting metadata-only extraction...")
        extractor.run_extraction(
            max_workflows=5,
            interactive_auth=False,  # No auth needed for metadata
            extract_json=False  # Skip JSON extraction
        )
        
        # Show results
        print("\n📊 Metadata-Only Results:")
        demo_path = Path(demo_dir)
        
        if demo_path.exists():
            metadata_files = list((demo_path / "metadata").glob("*.json"))
            print(f"✅ Metadata files created: {len(metadata_files)}")
            
            # Show sample metadata
            if metadata_files:
                sample_file = metadata_files[0]
                with open(sample_file, 'r', encoding='utf-8') as f:
                    sample_data = json.load(f)
                
                print(f"\n📄 Sample metadata from {sample_file.name}:")
                print(f"   Title: {sample_data.get('title', 'N/A')}")
                print(f"   Author: {sample_data.get('author', 'N/A')}")
                print(f"   Category: {sample_data.get('category', 'N/A')}")
                print(f"   Is Free: {sample_data.get('is_free', 'N/A')}")
                print(f"   View Count: {sample_data.get('view_count', 'N/A')}")
                print(f"   Has JSON: {sample_data.get('has_json', 'N/A')}")
        
        print("\n✅ Metadata-only extraction completed!")
        
    except Exception as e:
        print(f"❌ Metadata-only extraction failed: {e}")

def demo_database_integration():
    """Demonstrate database integration with extracted data"""
    print("\n🚀 Demo: Database Integration")
    print("=" * 50)
    
    # Check if we have any extracted data
    demo_dirs = ["demo_extraction", "demo_metadata_only"]
    data_file = None
    
    for demo_dir in demo_dirs:
        potential_file = Path(demo_dir) / "workflows_data.json"
        if potential_file.exists():
            data_file = potential_file
            break
    
    if not data_file:
        print("❌ No extracted data found. Please run extraction demo first.")
        return
    
    print(f"📄 Using data from: {data_file}")
    
    try:
        # Initialize database
        kb = WorkflowKnowledgeBase(
            db_path="demo_workflows.db",
            vector_path="demo_vector_store.pkl"
        )
        
        # Import extracted data
        print("\n🔄 Importing extracted workflows into database...")
        count = kb.add_workflows_from_json(str(data_file))
        print(f"✅ Imported {count} workflows into database")
        
        # Demonstrate search
        print("\n🔍 Testing search functionality...")
        
        # Get all workflows
        all_workflows = kb.db.search_workflows(limit=50)
        print(f"📊 Total workflows in database: {len(all_workflows)}")
        
        if all_workflows:
            # Show sample workflow
            sample = all_workflows[0]
            print(f"\n📄 Sample workflow:")
            print(f"   ID: {sample.get('id', 'N/A')}")
            print(f"   Title: {sample.get('title', 'N/A')}")
            print(f"   Category: {sample.get('category', 'N/A')}")
            print(f"   Is Free: {sample.get('is_free', 'N/A')}")
            
            # Test semantic search if we have data
            if len(all_workflows) > 1:
                search_results = kb.search("automation", use_semantic=True, limit=3)
                print(f"\n🔍 Search results for 'automation': {len(search_results)} found")
                
                for i, result in enumerate(search_results[:2]):
                    similarity = result.get('similarity_score', 0)
                    print(f"   {i+1}. {result.get('title', 'N/A')} (similarity: {similarity:.3f})")
        
        # Show database stats
        stats = kb.db.get_stats()
        print(f"\n📊 Database Statistics:")
        print(f"   Total workflows: {stats.get('total_workflows', 0)}")
        print(f"   Free workflows: {stats.get('free_workflows', 0)}")
        print(f"   Paid workflows: {stats.get('paid_workflows', 0)}")
        print(f"   Categories: {len(stats.get('categories', {}))}")
        
        print("\n✅ Database integration demo completed!")
        
        # Clean up demo database files
        cleanup_response = input("\nClean up demo database files? (y/N): ")
        if cleanup_response.lower() == 'y':
            for file in ["demo_workflows.db", "demo_vector_store.pkl"]:
                if Path(file).exists():
                    os.unlink(file)
                    print(f"🗑️ Removed {file}")
        
    except Exception as e:
        print(f"❌ Database integration demo failed: {e}")

def demo_file_analysis():
    """Analyze extracted files to show what was downloaded"""
    print("\n🚀 Demo: File Analysis")
    print("=" * 50)
    
    demo_dirs = ["demo_extraction", "demo_metadata_only"]
    
    for demo_dir in demo_dirs:
        demo_path = Path(demo_dir)
        if not demo_path.exists():
            continue
        
        print(f"\n📁 Analyzing {demo_dir}:")
        
        # Analyze JSON files
        json_dir = demo_path / "json_files"
        if json_dir.exists():
            json_files = list(json_dir.glob("*.json"))
            print(f"   📄 JSON files: {len(json_files)}")
            
            if json_files:
                # Analyze a sample JSON file
                sample_json = json_files[0]
                try:
                    with open(sample_json, 'r', encoding='utf-8') as f:
                        json_data = json.load(f)
                    
                    print(f"   📄 Sample JSON structure from {sample_json.name}:")
                    print(f"      Keys: {list(json_data.keys())}")
                    
                    if 'nodes' in json_data:
                        print(f"      Nodes: {len(json_data['nodes'])}")
                    if 'edges' in json_data:
                        print(f"      Edges: {len(json_data['edges'])}")
                        
                except Exception as e:
                    print(f"      ❌ Error reading JSON: {e}")
        
        # Analyze metadata files
        metadata_dir = demo_path / "metadata"
        if metadata_dir.exists():
            metadata_files = list(metadata_dir.glob("*.json"))
            print(f"   📄 Metadata files: {len(metadata_files)}")
        
        # Analyze log files
        logs_dir = demo_path / "logs"
        if logs_dir.exists():
            log_files = list(logs_dir.glob("*.json"))
            print(f"   📄 Log files: {len(log_files)}")
            
            if log_files:
                # Show latest stats
                latest_log = max(log_files, key=lambda f: f.stat().st_mtime)
                try:
                    with open(latest_log, 'r', encoding='utf-8') as f:
                        stats = json.load(f)
                    
                    print(f"   📊 Latest extraction stats:")
                    print(f"      Total processed: {stats.get('total_processed', 0)}")
                    print(f"      JSON downloaded: {stats.get('json_downloaded', 0)}")
                    print(f"      Paid skipped: {stats.get('paid_skipped', 0)}")
                    print(f"      Success rate: {stats.get('success_rate', 0):.1f}%")
                    
                except Exception as e:
                    print(f"      ❌ Error reading stats: {e}")

def cleanup_demo_files():
    """Clean up demo files"""
    print("\n🧹 Cleanup Demo Files")
    print("=" * 50)
    
    demo_dirs = ["demo_extraction", "demo_metadata_only"]
    
    response = input("Would you like to clean up all demo files? (y/N): ")
    if response.lower() != 'y':
        print("Cleanup cancelled.")
        return
    
    import shutil
    
    for demo_dir in demo_dirs:
        if Path(demo_dir).exists():
            try:
                shutil.rmtree(demo_dir)
                print(f"🗑️ Removed directory: {demo_dir}")
            except Exception as e:
                print(f"❌ Error removing {demo_dir}: {e}")
    
    # Remove any remaining demo database files
    for file in ["demo_workflows.db", "demo_vector_store.pkl"]:
        if Path(file).exists():
            try:
                os.unlink(file)
                print(f"🗑️ Removed file: {file}")
            except Exception as e:
                print(f"❌ Error removing {file}: {e}")
    
    print("✅ Cleanup completed!")

def main():
    """Main demo menu"""
    print("🎉 Enhanced AgentChef Extractor - Interactive Demo")
    print("=" * 60)
    print()
    print("This demo will show you the enhanced features:")
    print("• Metadata + JSON extraction")
    print("• Paid workflow detection and skipping")
    print("• Organized file storage")
    print("• Database integration")
    print("• Comprehensive statistics")
    print()
    
    while True:
        print("\n📋 Demo Options:")
        print("1. Basic extraction demo (3 workflows)")
        print("2. Metadata-only extraction (5 workflows)")
        print("3. Database integration demo")
        print("4. File analysis")
        print("5. Cleanup demo files")
        print("6. Exit")
        
        choice = input("\nSelect an option (1-6): ").strip()
        
        if choice == '1':
            demo_basic_extraction()
        elif choice == '2':
            demo_metadata_only()
        elif choice == '3':
            demo_database_integration()
        elif choice == '4':
            demo_file_analysis()
        elif choice == '5':
            cleanup_demo_files()
        elif choice == '6':
            print("\n👋 Thanks for trying the enhanced extractor!")
            break
        else:
            print("❌ Invalid option. Please select 1-6.")

if __name__ == "__main__":
    main()
