#!/usr/bin/env python3
"""
Debug and fix clipboard extraction for AgentChef workflow JSON
"""

import time
import json
from pathlib import Path
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.action_chains import ActionChains
from webdriver_manager.chrome import ChromeDriverManager

def setup_browser():
    """Setup browser for clipboard testing"""
    chrome_options = Options()
    # Don't use headless mode for clipboard testing
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-web-security')
    chrome_options.add_argument('--allow-running-insecure-content')
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    return driver

def test_clipboard_extraction_methods(driver, workflow_id):
    """Test different clipboard extraction methods"""
    workflow_url = f"https://agentchef.vercel.app/workflow/{workflow_id}"
    print(f"🌐 Navigating to: {workflow_url}")
    driver.get(workflow_url)
    time.sleep(5)
    
    print("🔍 Looking for 'Copy Workflow JSON' button...")
    
    # Find the copy button
    copy_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Copy Workflow JSON')]")
    
    if not copy_buttons:
        print("❌ No 'Copy Workflow JSON' button found")
        return None
    
    copy_button = copy_buttons[0]
    print(f"✅ Found copy button: {copy_button.text}")
    
    # Method 1: Direct click and immediate clipboard read
    print("\n🧪 Method 1: Direct click + immediate clipboard read")
    try:
        copy_button.click()
        time.sleep(1)
        
        # Try to read clipboard using JavaScript
        clipboard_content = driver.execute_script("""
            return navigator.clipboard.readText().then(text => {
                return text;
            }).catch(err => {
                console.log('Clipboard read failed:', err);
                return null;
            });
        """)
        
        if clipboard_content:
            print(f"✅ Method 1 success! Content length: {len(clipboard_content)}")
            print(f"📄 First 200 chars: {clipboard_content[:200]}...")
            return clipboard_content
        else:
            print("❌ Method 1 failed - no clipboard content")
    except Exception as e:
        print(f"❌ Method 1 error: {e}")
    
    # Method 2: Click + textarea paste
    print("\n🧪 Method 2: Click + textarea paste")
    try:
        copy_button.click()
        time.sleep(1)
        
        # Create textarea and paste
        driver.execute_script("""
            var textarea = document.createElement('textarea');
            textarea.id = 'clipboard-test';
            textarea.style.position = 'fixed';
            textarea.style.top = '0';
            textarea.style.left = '0';
            textarea.style.width = '1px';
            textarea.style.height = '1px';
            textarea.style.opacity = '0';
            document.body.appendChild(textarea);
            textarea.focus();
            textarea.select();
        """)
        
        time.sleep(0.5)
        
        # Find textarea and paste
        textarea = driver.find_element(By.ID, "clipboard-test")
        textarea.send_keys(Keys.CONTROL + "v")
        time.sleep(1)
        
        content = textarea.get_attribute("value")
        
        # Clean up
        driver.execute_script("document.getElementById('clipboard-test').remove();")
        
        if content and content.strip():
            print(f"✅ Method 2 success! Content length: {len(content)}")
            print(f"📄 First 200 chars: {content[:200]}...")
            return content
        else:
            print("❌ Method 2 failed - no content in textarea")
    except Exception as e:
        print(f"❌ Method 2 error: {e}")
    
    # Method 3: Manual intervention
    print("\n🧪 Method 3: Manual verification")
    print("Please manually click the 'Copy Workflow JSON' button and then press Ctrl+V in a text editor")
    print("to verify what's actually being copied to clipboard.")
    input("Press Enter after you've verified the clipboard content...")
    
    return None

def save_workflow_json(workflow_id, json_content, output_dir="debug_json_output"):
    """Save the extracted JSON content"""
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # Try to parse as JSON first
    try:
        parsed_json = json.loads(json_content)
        print("✅ Content is valid JSON")
        
        # Save formatted JSON
        json_file = output_path / f"{workflow_id}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(parsed_json, f, indent=2, ensure_ascii=False)
        
        print(f"💾 Saved JSON to: {json_file}")
        print(f"📊 JSON structure: {list(parsed_json.keys()) if isinstance(parsed_json, dict) else 'Not a dict'}")
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ Content is not valid JSON: {e}")
        
        # Save as text file for inspection
        text_file = output_path / f"{workflow_id}_raw.txt"
        with open(text_file, 'w', encoding='utf-8') as f:
            f.write(json_content)
        
        print(f"💾 Saved raw content to: {text_file}")
        return False

def main():
    print("🔧 AgentChef Clipboard Extraction Debugger")
    print("=" * 50)
    
    # Test with a known workflow
    test_workflow_id = "3da8e99e-bf72-42f8-a4ca-eb9e9737baca"
    
    print(f"🎯 Testing with workflow: {test_workflow_id}")
    print("🔐 Make sure you're logged in to AgentChef!")
    print()
    
    input("Press Enter to start browser and begin testing...")
    
    driver = None
    try:
        driver = setup_browser()
        print("✅ Browser started")
        
        # Navigate to AgentChef first for login
        print("🔐 Please log in to AgentChef...")
        driver.get("https://agentchef.vercel.app")
        input("Press Enter after you've logged in...")
        
        # Test clipboard extraction
        json_content = test_clipboard_extraction_methods(driver, test_workflow_id)
        
        if json_content:
            print("\n💾 Attempting to save extracted content...")
            success = save_workflow_json(test_workflow_id, json_content)
            
            if success:
                print("🎉 Successfully extracted and saved workflow JSON!")
            else:
                print("⚠️ Content extracted but not valid JSON - saved for inspection")
        else:
            print("❌ No content extracted from clipboard")
        
        print("\n🔍 Browser will stay open for 30 seconds for manual inspection...")
        time.sleep(30)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if driver:
            driver.quit()

if __name__ == "__main__":
    main()
