2025-06-30 12:15:09,978 - INFO - ====== WebDriver manager ======
2025-06-30 12:15:10,943 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-30 12:15:11,218 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-30 12:15:11,461 - INFO - There is no [win64] chromedriver "137.0.7151.119" for browser google-chrome "137.0.7151" in cache
2025-06-30 12:15:11,462 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-30 12:15:12,052 - INFO - WebDriver version 137.0.7151.119 selected
2025-06-30 12:15:12,058 - INFO - Modern chrome version https://storage.googleapis.com/chrome-for-testing-public/137.0.7151.119/win32/chromedriver-win32.zip
2025-06-30 12:15:12,060 - INFO - About to download new driver from https://storage.googleapis.com/chrome-for-testing-public/137.0.7151.119/win32/chromedriver-win32.zip
2025-06-30 12:15:12,328 - INFO - Driver downloading response is 200
2025-06-30 12:15:12,924 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-30 12:15:13,952 - INFO - Driver has been saved in cache [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.119]
2025-06-30 12:15:15,191 - INFO - Selenium WebDriver initialized successfully with download capabilities
2025-06-30 12:15:17,282 - INFO - Selenium WebDriver closed
2025-06-30 12:15:17,289 - INFO - Saved JSON file: test_json_ops\json_files\test-workflow-123.json
2025-06-30 12:15:17,302 - INFO - Saved extraction statistics to test_stats\logs\extraction_stats_20250630_121517.json
2025-06-30 12:15:17,346 - INFO - Database initialized at test_integration.db
2025-06-30 12:15:17,364 - INFO - Saved vector store with 1 embeddings
2025-06-30 12:15:17,364 - INFO - Added 1 workflows to knowledge base
